<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlantUML Editor Replacer</title>
</head>
<body>
            <iframe 
                id="plantumlFrame" 
                src="https://editor.plantuml.com/uml/" 
                style="display: none;">
            </iframe>
            
            <div class="replaced-content" id="replacedContent">
                <html lang="en"><head><style id="ace-github">.ace-github .ace_gutter {background: #e8e8e8;color: #AAA;}.ace-github  {background: #fff;color: #000;}.ace-github .ace_keyword {font-weight: bold;}.ace-github .ace_string {color: #D14;}.ace-github .ace_variable.ace_class {color: teal;}.ace-github .ace_constant.ace_numeric {color: #099;}.ace-github .ace_constant.ace_buildin {color: #0086B3;}.ace-github .ace_support.ace_function {color: #0086B3;}.ace-github .ace_comment {color: #998;font-style: italic;}.ace-github .ace_variable.ace_language  {color: #0086B3;}.ace-github .ace_paren {font-weight: bold;}.ace-github .ace_boolean {font-weight: bold;}.ace-github .ace_string.ace_regexp {color: #009926;font-weight: normal;}.ace-github .ace_variable.ace_instance {color: teal;}.ace-github .ace_constant.ace_language {font-weight: bold;}.ace-github .ace_cursor {color: black;}.ace-github.ace_focus .ace_marker-layer .ace_active-line {background: rgb(255, 255, 204);}.ace-github .ace_marker-layer .ace_active-line {background: rgb(245, 245, 245);}.ace-github .ace_marker-layer .ace_selection {background: rgb(181, 213, 255);}.ace-github.ace_multiselect .ace_selection.ace_start {box-shadow: 0 0 3px 0px white;}.ace-github.ace_nobold .ace_line > span {font-weight: normal !important;}.ace-github .ace_marker-layer .ace_step {background: rgb(252, 255, 0);}.ace-github .ace_marker-layer .ace_stack {background: rgb(164, 229, 101);}.ace-github .ace_marker-layer .ace_bracket {margin: -1px 0 0 -1px;border: 1px solid rgb(192, 192, 192);}.ace-github .ace_gutter-active-line {background-color : rgba(0, 0, 0, 0.07);}.ace-github .ace_marker-layer .ace_selected-word {background: rgb(250, 250, 255);border: 1px solid rgb(200, 200, 250);}.ace-github .ace_invisible {color: #BFBFBF}.ace-github .ace_print-margin {width: 1px;background: #e8e8e8;}.ace-github .ace_indent-guide {background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==") right repeat-y;}
                    /*# sourceURL=ace/css/ace-github */</style><style id="autocompletion.css">.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {    background-color: #CAD6FA;    z-index: 1;}.ace_dark.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {    background-color: #3a674e;}.ace_editor.ace_autocomplete .ace_line-hover {    border: 1px solid #abbffe;    margin-top: -1px;    background: rgba(233,233,253,0.4);    position: absolute;    z-index: 2;}.ace_dark.ace_editor.ace_autocomplete .ace_line-hover {    border: 1px solid rgba(109, 150, 13, 0.8);    background: rgba(58, 103, 78, 0.62);}.ace_completion-meta {    opacity: 0.5;    margin: 0.9em;}.ace_completion-message {    color: blue;}.ace_editor.ace_autocomplete .ace_completion-highlight{    color: #2d69c7;}.ace_dark.ace_editor.ace_autocomplete .ace_completion-highlight{    color: #93ca12;}.ace_editor.ace_autocomplete {    width: 300px;    z-index: 200000;    border: 1px lightgray solid;    position: fixed;    box-shadow: 2px 3px 5px rgba(0,0,0,.2);    line-height: 1.4;    background: #fefefe;    color: #111;}.ace_dark.ace_editor.ace_autocomplete {    border: 1px #484747 solid;    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.51);    line-height: 1.4;    background: #25282c;    color: #c1c1c1;}
                    /*# sourceURL=ace/css/autocompletion.css */</style><style id="snippets.css">.ace_snippet-marker {    -moz-box-sizing: border-box;    box-sizing: border-box;    background: rgba(194, 193, 208, 0.09);    border: 1px dotted rgba(211, 208, 235, 0.62);    position: absolute;}
                    /*# sourceURL=ace/css/snippets.css */</style><style id="error_marker.css">    .error_widget_wrapper {        background: inherit;        color: inherit;        border:none    }    .error_widget {        border-top: solid 2px;        border-bottom: solid 2px;        margin: 5px 0;        padding: 10px 40px;        white-space: pre-wrap;    }    .error_widget.ace_error, .error_widget_arrow.ace_error{        border-color: #ff5a5a    }    .error_widget.ace_warning, .error_widget_arrow.ace_warning{        border-color: #F1D817    }    .error_widget.ace_info, .error_widget_arrow.ace_info{        border-color: #5a5a5a    }    .error_widget.ace_ok, .error_widget_arrow.ace_ok{        border-color: #5aaa5a    }    .error_widget_arrow {        position: absolute;        border: solid 5px;        border-top-color: transparent!important;        border-right-color: transparent!important;        border-left-color: transparent!important;        top: -5px;    }
                    /*# sourceURL=ace/css/error_marker.css */</style><style id="ace-tm">.ace-tm .ace_gutter {background: #f0f0f0;color: #333;}.ace-tm .ace_print-margin {width: 1px;background: #e8e8e8;}.ace-tm .ace_fold {background-color: #6B72E6;}.ace-tm {background-color: #FFFFFF;color: black;}.ace-tm .ace_cursor {color: black;}.ace-tm .ace_invisible {color: rgb(191, 191, 191);}.ace-tm .ace_storage,.ace-tm .ace_keyword {color: blue;}.ace-tm .ace_constant {color: rgb(197, 6, 11);}.ace-tm .ace_constant.ace_buildin {color: rgb(88, 72, 246);}.ace-tm .ace_constant.ace_language {color: rgb(88, 92, 246);}.ace-tm .ace_constant.ace_library {color: rgb(6, 150, 14);}.ace-tm .ace_invalid {background-color: rgba(255, 0, 0, 0.1);color: red;}.ace-tm .ace_support.ace_function {color: rgb(60, 76, 114);}.ace-tm .ace_support.ace_constant {color: rgb(6, 150, 14);}.ace-tm .ace_support.ace_type,.ace-tm .ace_support.ace_class {color: rgb(109, 121, 222);}.ace-tm .ace_keyword.ace_operator {color: rgb(104, 118, 135);}.ace-tm .ace_string {color: rgb(3, 106, 7);}.ace-tm .ace_comment {color: rgb(76, 136, 107);}.ace-tm .ace_comment.ace_doc {color: rgb(0, 102, 255);}.ace-tm .ace_comment.ace_doc.ace_tag {color: rgb(128, 159, 191);}.ace-tm .ace_constant.ace_numeric {color: rgb(0, 0, 205);}.ace-tm .ace_variable {color: rgb(49, 132, 149);}.ace-tm .ace_xml-pe {color: rgb(104, 104, 91);}.ace-tm .ace_entity.ace_name.ace_function {color: #0000A2;}.ace-tm .ace_heading {color: rgb(12, 7, 255);}.ace-tm .ace_list {color:rgb(185, 6, 144);}.ace-tm .ace_meta.ace_tag {color:rgb(0, 22, 142);}.ace-tm .ace_string.ace_regex {color: rgb(255, 0, 0)}.ace-tm .ace_marker-layer .ace_selection {background: rgb(181, 213, 255);}.ace-tm.ace_multiselect .ace_selection.ace_start {box-shadow: 0 0 3px 0px white;}.ace-tm .ace_marker-layer .ace_step {background: rgb(252, 255, 0);}.ace-tm .ace_marker-layer .ace_stack {background: rgb(164, 229, 101);}.ace-tm .ace_marker-layer .ace_bracket {margin: -1px 0 0 -1px;border: 1px solid rgb(192, 192, 192);}.ace-tm .ace_marker-layer .ace_active-line {background: rgba(0, 0, 0, 0.07);}.ace-tm .ace_gutter-active-line {background-color : #dcdcdc;}.ace-tm .ace_marker-layer .ace_selected-word {background: rgb(250, 250, 255);border: 1px solid rgb(200, 200, 250);}.ace-tm .ace_indent-guide {background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==") right repeat-y;}
                    /*# sourceURL=ace/css/ace-tm */</style><style id="ace_editor.css">.ace_br1 {border-top-left-radius    : 3px;}.ace_br2 {border-top-right-radius   : 3px;}.ace_br3 {border-top-left-radius    : 3px; border-top-right-radius:    3px;}.ace_br4 {border-bottom-right-radius: 3px;}.ace_br5 {border-top-left-radius    : 3px; border-bottom-right-radius: 3px;}.ace_br6 {border-top-right-radius   : 3px; border-bottom-right-radius: 3px;}.ace_br7 {border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px;}.ace_br8 {border-bottom-left-radius : 3px;}.ace_br9 {border-top-left-radius    : 3px; border-bottom-left-radius:  3px;}.ace_br10{border-top-right-radius   : 3px; border-bottom-left-radius:  3px;}.ace_br11{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-left-radius:  3px;}.ace_br12{border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}.ace_br13{border-top-left-radius    : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}.ace_br14{border-top-right-radius   : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}.ace_br15{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;}.ace_editor {position: relative;overflow: hidden;padding: 0;font: 12px/normal 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;direction: ltr;text-align: left;-webkit-tap-highlight-color: rgba(0, 0, 0, 0);}.ace_scroller {position: absolute;overflow: hidden;top: 0;bottom: 0;background-color: inherit;-ms-user-select: none;-moz-user-select: none;-webkit-user-select: none;user-select: none;cursor: text;}.ace_content {position: absolute;box-sizing: border-box;min-width: 100%;contain: style size layout;font-variant-ligatures: no-common-ligatures;}.ace_dragging .ace_scroller:before{position: absolute;top: 0;left: 0;right: 0;bottom: 0;content: '';background: rgba(250, 250, 250, 0.01);z-index: 1000;}.ace_dragging.ace_dark .ace_scroller:before{background: rgba(0, 0, 0, 0.01);}.ace_selecting, .ace_selecting * {cursor: text !important;}.ace_gutter {position: absolute;overflow : hidden;width: auto;top: 0;bottom: 0;left: 0;cursor: default;z-index: 4;-ms-user-select: none;-moz-user-select: none;-webkit-user-select: none;user-select: none;contain: style size layout;}.ace_gutter-active-line {position: absolute;left: 0;right: 0;}.ace_scroller.ace_scroll-left {box-shadow: 17px 0 16px -16px rgba(0, 0, 0, 0.4) inset;}.ace_gutter-cell {position: absolute;top: 0;left: 0;right: 0;padding-left: 19px;padding-right: 6px;background-repeat: no-repeat;}.ace_gutter-cell.ace_error {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAABOFBMVEX/////////QRswFAb/Ui4wFAYwFAYwFAaWGAfDRymzOSH/PxswFAb/SiUwFAYwFAbUPRvjQiDllog5HhHdRybsTi3/Tyv9Tir+Syj/UC3////XurebMBIwFAb/RSHbPx/gUzfdwL3kzMivKBAwFAbbvbnhPx66NhowFAYwFAaZJg8wFAaxKBDZurf/RB6mMxb/SCMwFAYwFAbxQB3+RB4wFAb/Qhy4Oh+4QifbNRcwFAYwFAYwFAb/QRzdNhgwFAYwFAbav7v/Uy7oaE68MBK5LxLewr/r2NXewLswFAaxJw4wFAbkPRy2PyYwFAaxKhLm1tMwFAazPiQwFAaUGAb/QBrfOx3bvrv/VC/maE4wFAbRPBq6MRO8Qynew8Dp2tjfwb0wFAbx6eju5+by6uns4uH9/f36+vr/GkHjAAAAYnRSTlMAGt+64rnWu/bo8eAA4InH3+DwoN7j4eLi4xP99Nfg4+b+/u9B/eDs1MD1mO7+4PHg2MXa347g7vDizMLN4eG+Pv7i5evs/v79yu7S3/DV7/498Yv24eH+4ufQ3Ozu/v7+y13sRqwAAADLSURBVHjaZc/XDsFgGIBhtDrshlitmk2IrbHFqL2pvXf/+78DPokj7+Fz9qpU/9UXJIlhmPaTaQ6QPaz0mm+5gwkgovcV6GZzd5JtCQwgsxoHOvJO15kleRLAnMgHFIESUEPmawB9ngmelTtipwwfASilxOLyiV5UVUyVAfbG0cCPHig+GBkzAENHS0AstVF6bacZIOzgLmxsHbt2OecNgJC83JERmePUYq8ARGkJx6XtFsdddBQgZE2nPR6CICZhawjA4Fb/chv+399kfR+MMMDGOQAAAABJRU5ErkJggg==");background-repeat: no-repeat;background-position: 2px center;}.ace_gutter-cell.ace_warning {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAmVBMVEX///8AAAD///8AAAAAAABPSzb/5sAAAAB/blH/73z/ulkAAAAAAAD85pkAAAAAAAACAgP/vGz/rkDerGbGrV7/pkQICAf////e0IsAAAD/oED/qTvhrnUAAAD/yHD/njcAAADuv2r/nz//oTj/p064oGf/zHAAAAA9Nir/tFIAAAD/tlTiuWf/tkIAAACynXEAAAAAAAAtIRW7zBpBAAAAM3RSTlMAABR1m7RXO8Ln31Z36zT+neXe5OzooRDfn+TZ4p3h2hTf4t3k3ucyrN1K5+Xaks52Sfs9CXgrAAAAjklEQVR42o3PbQ+CIBQFYEwboPhSYgoYunIqqLn6/z8uYdH8Vmdnu9vz4WwXgN/xTPRD2+sgOcZjsge/whXZgUaYYvT8QnuJaUrjrHUQreGczuEafQCO/SJTufTbroWsPgsllVhq3wJEk2jUSzX3CUEDJC84707djRc5MTAQxoLgupWRwW6UB5fS++NV8AbOZgnsC7BpEAAAAABJRU5ErkJggg==");background-position: 2px center;}.ace_gutter-cell.ace_info {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAAAAAA6mKC9AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAJ0Uk5TAAB2k804AAAAPklEQVQY02NgIB68QuO3tiLznjAwpKTgNyDbMegwisCHZUETUZV0ZqOquBpXj2rtnpSJT1AEnnRmL2OgGgAAIKkRQap2htgAAAAASUVORK5CYII=");background-position: 2px center;}.ace_dark .ace_gutter-cell.ace_info {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAJFBMVEUAAAChoaGAgIAqKiq+vr6tra1ZWVmUlJSbm5s8PDxubm56enrdgzg3AAAAAXRSTlMAQObYZgAAAClJREFUeNpjYMAPdsMYHegyJZFQBlsUlMFVCWUYKkAZMxZAGdxlDMQBAG+TBP4B6RyJAAAAAElFTkSuQmCC");}.ace_scrollbar {contain: strict;position: absolute;right: 0;bottom: 0;z-index: 6;}.ace_scrollbar-inner {position: absolute;cursor: text;left: 0;top: 0;}.ace_scrollbar-v{overflow-x: hidden;overflow-y: scroll;top: 0;}.ace_scrollbar-h {overflow-x: scroll;overflow-y: hidden;left: 0;}.ace_print-margin {position: absolute;height: 100%;}.ace_text-input {position: absolute;z-index: 0;width: 0.5em;height: 1em;opacity: 0;background: transparent;-moz-appearance: none;appearance: none;border: none;resize: none;outline: none;overflow: hidden;font: inherit;padding: 0 1px;margin: 0 -1px;contain: strict;-ms-user-select: text;-moz-user-select: text;-webkit-user-select: text;user-select: text;white-space: pre!important;}.ace_text-input.ace_composition {background: transparent;color: inherit;z-index: 1000;opacity: 1;}.ace_composition_placeholder { color: transparent }.ace_composition_marker { border-bottom: 1px solid;position: absolute;border-radius: 0;margin-top: 1px;}[ace_nocontext=true] {transform: none!important;filter: none!important;clip-path: none!important;mask : none!important;contain: none!important;perspective: none!important;mix-blend-mode: initial!important;z-index: auto;}.ace_layer {z-index: 1;position: absolute;overflow: hidden;word-wrap: normal;white-space: pre;height: 100%;width: 100%;box-sizing: border-box;pointer-events: none;}.ace_gutter-layer {position: relative;width: auto;text-align: right;pointer-events: auto;height: 1000000px;contain: style size layout;}.ace_text-layer {font: inherit !important;position: absolute;height: 1000000px;width: 1000000px;contain: style size layout;}.ace_text-layer > .ace_line, .ace_text-layer > .ace_line_group {contain: style size layout;position: absolute;top: 0;left: 0;right: 0;}.ace_hidpi .ace_text-layer,.ace_hidpi .ace_gutter-layer,.ace_hidpi .ace_content,.ace_hidpi .ace_gutter {contain: strict;will-change: transform;}.ace_hidpi .ace_text-layer > .ace_line, .ace_hidpi .ace_text-layer > .ace_line_group {contain: strict;}.ace_cjk {display: inline-block;text-align: center;}.ace_cursor-layer {z-index: 4;}.ace_cursor {z-index: 4;position: absolute;box-sizing: border-box;border-left: 2px solid;transform: translatez(0);}.ace_multiselect .ace_cursor {border-left-width: 1px;}.ace_slim-cursors .ace_cursor {border-left-width: 1px;}.ace_overwrite-cursors .ace_cursor {border-left-width: 0;border-bottom: 1px solid;}.ace_hidden-cursors .ace_cursor {opacity: 0.2;}.ace_hasPlaceholder .ace_hidden-cursors .ace_cursor {opacity: 0;}.ace_smooth-blinking .ace_cursor {transition: opacity 0.18s;}.ace_animate-blinking .ace_cursor {animation-duration: 1000ms;animation-timing-function: step-end;animation-name: blink-ace-animate;animation-iteration-count: infinite;}.ace_animate-blinking.ace_smooth-blinking .ace_cursor {animation-duration: 1000ms;animation-timing-function: ease-in-out;animation-name: blink-ace-animate-smooth;}@keyframes blink-ace-animate {from, to { opacity: 1; }60% { opacity: 0; }}@keyframes blink-ace-animate-smooth {from, to { opacity: 1; }45% { opacity: 1; }60% { opacity: 0; }85% { opacity: 0; }}.ace_marker-layer .ace_step, .ace_marker-layer .ace_stack {position: absolute;z-index: 3;}.ace_marker-layer .ace_selection {position: absolute;z-index: 5;}.ace_marker-layer .ace_bracket {position: absolute;z-index: 6;}.ace_marker-layer .ace_error_bracket {position: absolute;border-bottom: 1px solid #DE5555;border-radius: 0;}.ace_marker-layer .ace_active-line {position: absolute;z-index: 2;}.ace_marker-layer .ace_selected-word {position: absolute;z-index: 4;box-sizing: border-box;}.ace_line .ace_fold {box-sizing: border-box;display: inline-block;height: 11px;margin-top: -2px;vertical-align: middle;background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACJJREFUeNpi+P//fxgTAwPDBxDxD078RSX+YeEyDFMCIMAAI3INmXiwf2YAAAAASUVORK5CYII=");background-repeat: no-repeat, repeat-x;background-position: center center, top left;color: transparent;border: 1px solid black;border-radius: 2px;cursor: pointer;pointer-events: auto;}.ace_dark .ace_fold {}.ace_fold:hover{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACBJREFUeNpi+P//fz4TAwPDZxDxD5X4i5fLMEwJgAADAEPVDbjNw87ZAAAAAElFTkSuQmCC");}.ace_tooltip {background-color: #FFF;background-image: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1));border: 1px solid gray;border-radius: 1px;box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);color: black;max-width: 100%;padding: 3px 4px;position: fixed;z-index: 999999;box-sizing: border-box;cursor: default;white-space: pre;word-wrap: break-word;line-height: normal;font-style: normal;font-weight: normal;letter-spacing: normal;pointer-events: none;}.ace_folding-enabled > .ace_gutter-cell {padding-right: 13px;}.ace_fold-widget {box-sizing: border-box;margin: 0 -12px 0 1px;display: none;width: 11px;vertical-align: top;background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42mWKsQ0AMAzC8ixLlrzQjzmBiEjp0A6WwBCSPgKAXoLkqSot7nN3yMwR7pZ32NzpKkVoDBUxKAAAAABJRU5ErkJggg==");background-repeat: no-repeat;background-position: center;border-radius: 3px;border: 1px solid transparent;cursor: pointer;}.ace_folding-enabled .ace_fold-widget {display: inline-block;   }.ace_fold-widget.ace_end {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42m3HwQkAMAhD0YzsRchFKI7sAikeWkrxwScEB0nh5e7KTPWimZki4tYfVbX+MNl4pyZXejUO1QAAAABJRU5ErkJggg==");}.ace_fold-widget.ace_closed {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAGCAYAAAAG5SQMAAAAOUlEQVR42jXKwQkAMAgDwKwqKD4EwQ26sSOkVWjgIIHAzPiCgaqiqnJHZnKICBERHN194O5b9vbLuAVRL+l0YWnZAAAAAElFTkSuQmCCXA==");}.ace_fold-widget:hover {border: 1px solid rgba(0, 0, 0, 0.3);background-color: rgba(255, 255, 255, 0.2);box-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);}.ace_fold-widget:active {border: 1px solid rgba(0, 0, 0, 0.4);background-color: rgba(0, 0, 0, 0.05);box-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);}.ace_dark .ace_fold-widget {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHklEQVQIW2P4//8/AzoGEQ7oGCaLLAhWiSwB146BAQCSTPYocqT0AAAAAElFTkSuQmCC");}.ace_dark .ace_fold-widget.ace_end {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAH0lEQVQIW2P4//8/AxQ7wNjIAjDMgC4AxjCVKBirIAAF0kz2rlhxpAAAAABJRU5ErkJggg==");}.ace_dark .ace_fold-widget.ace_closed {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAFCAYAAACAcVaiAAAAHElEQVQIW2P4//+/AxAzgDADlOOAznHAKgPWAwARji8UIDTfQQAAAABJRU5ErkJggg==");}.ace_dark .ace_fold-widget:hover {box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);background-color: rgba(255, 255, 255, 0.1);}.ace_dark .ace_fold-widget:active {box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);}.ace_inline_button {border: 1px solid lightgray;display: inline-block;margin: -1px 8px;padding: 0 5px;pointer-events: auto;cursor: pointer;}.ace_inline_button:hover {border-color: gray;background: rgba(200,200,200,0.2);display: inline-block;pointer-events: auto;}.ace_fold-widget.ace_invalid {background-color: #FFB4B4;border-color: #DE5555;}.ace_fade-fold-widgets .ace_fold-widget {transition: opacity 0.4s ease 0.05s;opacity: 0;}.ace_fade-fold-widgets:hover .ace_fold-widget {transition: opacity 0.05s ease 0.05s;opacity:1;}.ace_underline {text-decoration: underline;}.ace_bold {font-weight: bold;}.ace_nobold .ace_bold {font-weight: normal;}.ace_italic {font-style: italic;}.ace_error-marker {background-color: rgba(255, 0, 0,0.2);position: absolute;z-index: 9;}.ace_highlight-marker {background-color: rgba(255, 255, 0,0.2);position: absolute;z-index: 8;}.ace_mobile-menu {position: absolute;line-height: 1.5;border-radius: 4px;-ms-user-select: none;-moz-user-select: none;-webkit-user-select: none;user-select: none;background: white;box-shadow: 1px 3px 2px grey;border: 1px solid #dcdcdc;color: black;}.ace_dark > .ace_mobile-menu {background: #333;color: #ccc;box-shadow: 1px 3px 2px grey;border: 1px solid #444;}.ace_mobile-button {padding: 2px;cursor: pointer;overflow: hidden;}.ace_mobile-button:hover {background-color: #eee;opacity:1;}.ace_mobile-button:active {background-color: #ddd;}.ace_placeholder {font-family: arial;transform: scale(0.9);transform-origin: left;white-space: pre;opacity: 0.7;margin: 0 10px;}
                    /*# sourceURL=ace/css/ace_editor.css */</style><link rel="preconnect" href="https://aax.amazon-adsystem.com"><link rel="preload" href="https://config.aps.amazon-adsystem.com/configs/aa05931b-5308-4ea3-95a2-adf84f4ffde4" as="script" fetchpriority="high"><link rel="preload" href="https://client.aps.amazon-adsystem.com/publisher.js" as="script" fetchpriority="high"><script src="https://rules.quantcount.com/rules-p-31iz6hfFutd16.js" async=""></script><script src="https://secure.quantserve.com/quant.js" async="true"></script><script src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202505290101/pubads_impl_page_level_ads.js?cb=31092773"></script><script async="" src="https://secure.cdn.fastclick.net/js/cnvr-coreid/latest/coreid.min.js"></script><script async="" src="https://secure.cdn.fastclick.net/js/cnvr-launcher/latest/launcher.min.js"></script><script src="https://cdn.hadronid.net/hadron.js?url=https%3A%2F%2Feditor.plantuml.com%2Fuml%2F&amp;ref=&amp;_it=amazon&amp;partner_id=524"></script><script async="true" src="https://config.aps.amazon-adsystem.com/configs/aa05931b-5308-4ea3-95a2-adf84f4ffde4" fetchpriority="high"></script><script async="true" src="https://client.aps.amazon-adsystem.com/publisher.js" fetchpriority="high"></script><script>var __ezHttpConsent={setByCat:function(src,tagType,attributes,category,force,customSetScriptFn=null){var setScript=function(){if(force||window.ezTcfConsent[category]){if(typeof customSetScriptFn==='function'){customSetScriptFn();}else{var scriptElement=document.createElement(tagType);scriptElement.src=src;attributes.forEach(function(attr){for(var key in attr){if(attr.hasOwnProperty(key)){scriptElement.setAttribute(key,attr[key]);}}});var firstScript=document.getElementsByTagName(tagType)[0];firstScript.parentNode.insertBefore(scriptElement,firstScript);}}};if(force||(window.ezTcfConsent&&window.ezTcfConsent.loaded)){setScript();}else if(typeof getEzConsentData==="function"){getEzConsentData().then(function(ezTcfConsent){if(ezTcfConsent&&ezTcfConsent.loaded){setScript();}else{console.error("cannot get ez consent data");force=true;setScript();}});}else{force=true;setScript();console.error("getEzConsentData is not a function");}},};</script>
                    <script>var ezTcfConsent=window.ezTcfConsent?window.ezTcfConsent:{loaded:false,store_info:false,develop_and_improve_services:false,measure_ad_performance:false,measure_content_performance:false,select_basic_ads:false,create_ad_profile:false,select_personalized_ads:false,create_content_profile:false,select_personalized_content:false,understand_audiences:false,use_limited_data_to_select_content:false,};function getEzConsentData(){return new Promise(function(resolve){document.addEventListener("ezConsentEvent",function(event){var ezTcfConsent=event.detail.ezTcfConsent;resolve(ezTcfConsent);});});}</script>
                    <script>if(typeof _setEzCookies!=='function'){function _setEzCookies(ezConsentData){var cookies=window.ezCookieQueue;for(var i=0;i<cookies.length;i++){var cookie=cookies[i];if(ezConsentData&&ezConsentData.loaded&&ezConsentData[cookie.tcfCategory]){document.cookie=cookie.name+"="+cookie.value;}}}}
                    window.ezCookieQueue=window.ezCookieQueue||[];if(typeof addEzCookies!=='function'){function addEzCookies(arr){window.ezCookieQueue=[...window.ezCookieQueue,...arr];}}
                    addEzCookies([{name:"ezopvc_173770",value:"1; Path=/; Domain=plantuml.com; Expires=Sat, 31 May 2025 12:56:21 UTC",tcfCategory:"understand_audiences",isEzoic:"true",},{name:"ezoab_173770",value:"mod275-c; Path=/; Domain=plantuml.com; Max-Age=7200",tcfCategory:"store_info",isEzoic:"true",},{name:"active_template::173770",value:"pub_site.1748694381; Path=/; Domain=plantuml.com; Expires=Mon, 02 Jun 2025 12:26:21 UTC",tcfCategory:"store_info",isEzoic:"true",},{name:"ezoadgid_173770",value:"-1; Path=/; Domain=plantuml.com; Max-Age=1800",tcfCategory:"understand_audiences",isEzoic:"true",},{name:"ezosuibasgeneris-1",value:"dd939eb4-ba1f-4c34-7564-f0c3032c2a86; Path=/; Domain=plantuml.com; Expires=Sun, 31 May 2026 12:26:21 UTC; Secure; SameSite=None",tcfCategory:"understand_audiences",isEzoic:"true",}]);if(window.ezTcfConsent&&window.ezTcfConsent.loaded){_setEzCookies(window.ezTcfConsent);}else if(typeof getEzConsentData==="function"){getEzConsentData().then(function(ezTcfConsent){if(ezTcfConsent&&ezTcfConsent.loaded){_setEzCookies(window.ezTcfConsent);}else{console.error("cannot get ez consent data");_setEzCookies(window.ezTcfConsent);}});}else{console.error("getEzConsentData is not a function");_setEzCookies(window.ezTcfConsent);}</script><script type="text/javascript" data-ezscrex="false" data-cfasync="false">window._ezaq = Object.assign({"edge_cache_status":31,"edge_response_time":880,"url":"https://editor.plantuml.com/uml/"}, typeof window._ezaq !== "undefined" ? window._ezaq : {});</script><script type="text/javascript" data-ezscrex="false" data-cfasync="false">window._ezaq = Object.assign({"ab_test_id":"mod275-c"}, typeof window._ezaq !== "undefined" ? window._ezaq : {});window.__ez=window.__ez||{};window.__ez.tf={};</script><script data-ezscrex="false" data-cfasync="false" data-pagespeed-no-defer="">var __ez=__ez||{};__ez.stms=Date.now();__ez.evt={};__ez.script={};__ez.ck=__ez.ck||{};__ez.template={};__ez.template.isOrig=false;window.__ezScriptHost="//www.ezojs.com";__ez.queue=__ez.queue||function(){var e=0,i=0,t=[],n=!1,o=[],r=[],s=!0,a=function(e,i,n,o,r,s,a){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:window,d=this;this.name=e,this.funcName=i,this.parameters=null===n?null:w(n)?n:[n],this.isBlock=o,this.blockedBy=r,this.deleteWhenComplete=s,this.isError=!1,this.isComplete=!1,this.isInitialized=!1,this.proceedIfError=a,this.fWindow=l,this.isTimeDelay=!1,this.process=function(){f("... func = "+e),d.isInitialized=!0,d.isComplete=!0,f("... func.apply: "+e);var i=d.funcName.split("."),n=null,o=this.fWindow||window;i.length>3||(n=3===i.length?o[i[0]][i[1]][i[2]]:2===i.length?o[i[0]][i[1]]:o[d.funcName]),null!=n&&n.apply(null,this.parameters),!0===d.deleteWhenComplete&&delete t[e],!0===d.isBlock&&(f("----- F'D: "+d.name),m())}},l=function(e,i,t,n,o,r,s){var a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:window,l=this;this.name=e,this.path=i,this.async=o,this.defer=r,this.isBlock=t,this.blockedBy=n,this.isInitialized=!1,this.isError=!1,this.isComplete=!1,this.proceedIfError=s,this.fWindow=a,this.isTimeDelay=!1,this.isPath=function(e){return"/"===e[0]&&"/"!==e[1]},this.getSrc=function(e){return void 0!==window.__ezScriptHost&&this.isPath(e)&&"banger.js"!==this.name?window.__ezScriptHost+e:e},this.process=function(){l.isInitialized=!0,f("... file = "+e);var i=this.fWindow?this.fWindow.document:document,t=i.createElement("script");t.src=this.getSrc(this.path),!0===o?t.async=!0:!0===r&&(t.defer=!0),t.onerror=function(){var e={url:window.location.href,name:l.name,path:l.path,user_agent:window.navigator.userAgent};"undefined"!=typeof _ezaq&&(e.pageview_id=_ezaq.page_view_id);var i=encodeURIComponent(JSON.stringify(e)),t=new XMLHttpRequest;t.open("GET","//g.ezoic.net/ezqlog?d="+i,!0),t.send(),f("----- ERR'D: "+l.name),l.isError=!0,!0===l.isBlock&&m()},t.onreadystatechange=t.onload=function(){var e=t.readyState;f("----- F'D: "+l.name),e&&!/loaded|complete/.test(e)||(l.isComplete=!0,!0===l.isBlock&&m())},i.getElementsByTagName("head")[0].appendChild(t)}},d=function(e,i){this.name=e,this.path="",this.async=!1,this.defer=!1,this.isBlock=!1,this.blockedBy=[],this.isInitialized=!0,this.isError=!1,this.isComplete=i,this.proceedIfError=!1,this.isTimeDelay=!1,this.process=function(){}};function c(e,i,n,s,a,d,c,u,f){var m=new l(e,i,n,s,a,d,c,f);!0===u?o[e]=m:r[e]=m,t[e]=m,h(m)}function h(e){!0!==u(e)&&0!=s&&e.process()}function u(e){if(!0===e.isTimeDelay&&!1===n)return f(e.name+" blocked = TIME DELAY!"),!0;if(w(e.blockedBy))for(var i=0;i<e.blockedBy.length;i++){var o=e.blockedBy[i];if(!1===t.hasOwnProperty(o))return f(e.name+" blocked = "+o),!0;if(!0===e.proceedIfError&&!0===t[o].isError)return!1;if(!1===t[o].isComplete)return f(e.name+" blocked = "+o),!0}return!1}function f(e){var i=window.location.href,t=new RegExp("[?&]ezq=([^&#]*)","i").exec(i);"1"===(t?t[1]:null)&&console.debug(e)}function m(){++e>200||(f("let's go"),p(o),p(r))}function p(e){for(var i in e)if(!1!==e.hasOwnProperty(i)){var t=e[i];!0===t.isComplete||u(t)||!0===t.isInitialized||!0===t.isError?!0===t.isError?f(t.name+": error"):!0===t.isComplete?f(t.name+": complete already"):!0===t.isInitialized&&f(t.name+": initialized already"):t.process()}}function w(e){return"[object Array]"==Object.prototype.toString.call(e)}return window.addEventListener("load",(function(){setTimeout((function(){n=!0,f("TDELAY -----"),m()}),5e3)}),!1),{addFile:c,addFileOnce:function(e,i,n,o,r,s,a,l,d){t[e]||c(e,i,n,o,r,s,a,l,d)},addDelayFile:function(e,i){var n=new l(e,i,!1,[],!1,!1,!0);n.isTimeDelay=!0,f(e+" ...  FILE! TDELAY"),r[e]=n,t[e]=n,h(n)},addFunc:function(e,n,s,l,d,c,u,f,m,p){!0===c&&(e=e+"_"+i++);var w=new a(e,n,s,l,d,u,f,p);!0===m?o[e]=w:r[e]=w,t[e]=w,h(w)},addDelayFunc:function(e,i,n){var o=new a(e,i,n,!1,[],!0,!0);o.isTimeDelay=!0,f(e+" ...  FUNCTION! TDELAY"),r[e]=o,t[e]=o,h(o)},items:t,processAll:m,setallowLoad:function(e){s=e},markLoaded:function(e){if(e&&0!==e.length){if(e in t){var i=t[e];!0===i.isComplete?f(i.name+" "+e+": error loaded duplicate"):(i.isComplete=!0,i.isInitialized=!0)}else t[e]=new d(e,!0);f("markLoaded dummyfile: "+t[e].name)}},logWhatsBlocked:function(){for(var e in t)!1!==t.hasOwnProperty(e)&&u(t[e])}}}();__ez.evt.add=function(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n()},__ez.evt.remove=function(e,t,n){e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent?e.detachEvent("on"+t,n):delete e["on"+t]};__ez.script.add=function(e){var t=document.createElement("script");t.src=e,t.async=!0,t.type="text/javascript",document.getElementsByTagName("head")[0].appendChild(t)};__ez.dot={};__ez.queue.addFile('/detroitchicago/boise.js', '/detroitchicago/boise.js?gcb=195-2&cb=5', true, [], true, false, true, false);__ez.queue.addFile('/parsonsmaize/abilene.js', '/parsonsmaize/abilene.js?gcb=195-2&cb=dc112bb7ea', true, [], true, false, true, false);__ez.queue.addFile('/parsonsmaize/mulvane.js', '/parsonsmaize/mulvane.js?gcb=195-2&cb=e75e48eec0', true, ['/parsonsmaize/abilene.js'], true, false, true, false);__ez.queue.addFile('/detroitchicago/birmingham.js', '/detroitchicago/birmingham.js?gcb=195-2&cb=539c47377c', true, ['/parsonsmaize/abilene.js'], true, false, true, false);</script><script src="//www.ezojs.com/detroitchicago/boise.js?gcb=195-2&amp;cb=5" async=""></script><script src="//www.ezojs.com/parsonsmaize/abilene.js?gcb=195-2&amp;cb=dc112bb7ea" async=""></script>
                    <script data-ezscrex="false" data-cfasync="false">__ez.ssaf=[];__ez.sswp=4;__ez.ssv=865159;__ez.sshsdef=false;</script>
                    <script data-ezscrex="false" data-cfasync="false">(function(){if("function"===typeof window.CustomEvent)return!1;window.CustomEvent=function(c,a){a=a||{bubbles:!1,cancelable:!1,detail:null};var b=document.createEvent("CustomEvent");b.initCustomEvent(c,a.bubbles,a.cancelable,a.detail);return b}})();</script><script data-ezscrex="false" data-cfasync="false">__ez.queue.addFile('/detroitchicago/tulsa.js', '/detroitchicago/tulsa.js?gcb=195-2&cb=9', false, [], true, false, true, false);</script><script src="//www.ezojs.com/detroitchicago/tulsa.js?gcb=195-2&amp;cb=9" async=""></script>
                    <script data-ezscrex="false" type="text/javascript" data-cfasync="false">window._ezaq = Object.assign({"ad_cache_level":1,"adpicker_placement_cnt":0,"ai_placeholder_cache_level":1,"ai_placeholder_placement_cnt":-1,"domain":"plantuml.com","domain_id":173770,"ezcache_level":0,"ezcache_skip_code":14,"has_bad_image":0,"has_bad_words":0,"is_sitespeed":1,"lt_cache_level":0,"response_size":42683,"response_size_orig":32051,"response_time_orig":991,"template_id":134,"url":"https://editor.plantuml.com/uml/","word_count":32,"worst_bad_word_level":0}, typeof window._ezaq !== "undefined" ? window._ezaq : {});__ez.queue.markLoaded('ezaqBaseReady');</script>
                    <link rel="preload" as="script" href="//securepubads.g.doubleclick.net/tag/js/gpt.js">
                    <link rel="preload" as="script" href="//go.ezodn.com/hb/dall.js?cb=195-2-122">
                    <script type="text/javascript">(function(){function storageAvailable(type){var storage;try{storage=window[type];var x='__storage_test__';storage.setItem(x,x);storage.removeItem(x);return true;}
                    catch(e){return e instanceof DOMException&&(e.code===22||e.code===1014||e.name==='QuotaExceededError'||e.name==='NS_ERROR_DOM_QUOTA_REACHED')&&(storage&&storage.length!==0);}}
                    function remove_ama_config(){if(storageAvailable('localStorage')){localStorage.removeItem("google_ama_config");}}
                    remove_ama_config()})()</script>
                    <script type="text/javascript">var ezoicTestActive = true</script>
                    <script type="text/javascript" data-ezscrex="false" data-cfasync="false">
                    window.ezAnalyticsStatic = true;
                    
                    function analyticsAddScript(script) {
                        var ezDynamic = document.createElement('script');
                        ezDynamic.type = 'text/javascript';
                        ezDynamic.innerHTML = script;
                        document.head.appendChild(ezDynamic);
                    }
                    function getCookiesWithPrefix() {
                        var allCookies = document.cookie.split(';');
                        var cookiesWithPrefix = {};
                    
                        for (var i = 0; i < allCookies.length; i++) {
                            var cookie = allCookies[i].trim();
                    
                            for (var j = 0; j < arguments.length; j++) {
                                var prefix = arguments[j];
                                if (cookie.indexOf(prefix) === 0) {
                                    var cookieParts = cookie.split('=');
                                    var cookieName = cookieParts[0];
                                    var cookieValue = cookieParts.slice(1).join('=');
                                    cookiesWithPrefix[cookieName] = decodeURIComponent(cookieValue);
                                    break; // Once matched, no need to check other prefixes
                                }
                            }
                        }
                    
                        return cookiesWithPrefix;
                    }
                    function productAnalytics() {
                        var d = {"pr":[1,6,3],"aop":{"14":0,"26":0,"4":134,"7":0},"omd5":"9e41f8acccb727dc5717f5ffe732186c"};
                        d.u = _ezaq.url;
                        d.p = _ezaq.page_view_id;
                        d.v = _ezaq.visit_uuid;
                        d.ab = _ezaq.ab_test_id;
                        d.e = JSON.stringify(_ezaq);
                        d.ref = document.referrer;
                        d.c = getCookiesWithPrefix('active_template', 'ez', 'lp_');
                        if(typeof ez_utmParams !== 'undefined') {
                            d.utm = ez_utmParams;
                        }
                    
                        var dataText = JSON.stringify(d);
                        var xhr = new XMLHttpRequest();
                        xhr.open('POST','/ezais/analytics?cb=1', true);
                        xhr.onload = function () {
                            if (xhr.status!=200) {
                                return;
                            }
                    
                            if(document.readyState !== 'loading') {
                                analyticsAddScript(xhr.response);
                                return;
                            }
                    
                            var eventFunc = function() {
                                if(document.readyState === 'loading') {
                                    return;
                                }
                                document.removeEventListener('readystatechange', eventFunc, false);
                                analyticsAddScript(xhr.response);
                            };
                    
                            document.addEventListener('readystatechange', eventFunc, false);
                        };
                        xhr.setRequestHeader('Content-Type','text/plain');
                        xhr.send(dataText);
                    }
                    __ez.queue.addFunc("productAnalytics", "productAnalytics", null, true, ['ezaqBaseReady'], false, false, false, true);
                    </script><script type="text/javascript" data-ezscrex="false" data-cfasync="false" async="">
                    function productEzoicAds() {
                    if(window.ezDisableAds === true) {
                        return;
                    }
                    window.google_reactive_ads_global_state = {
                                    adCount: {},
                                    floatingAdsStacking: { maxZIndexListeners: [], maxZIndexRestrictions: {}, nextRestrictionId: 0 },
                                    messageValidationEnabled: false,
                                    reactiveTypeDisabledByPublisher: {},
                                    reactiveTypeEnabledInAsfe: {},
                                    sideRailAvailableSpace: [],
                                    sideRailOverlappableElements: [],
                                    stateForType: {},
                                    tagSpecificState: {},
                                    wasPlaTagProcessed: true,
                                    wasReactiveAdConfigReceived: { 1: true, 2: true, 8: true },
                                    wasReactiveAdVisible: {},
                                    wasReactiveTagRequestSent: true,
                                    description: "Can't disable auto ads programmatically on the page, so here we are!"
                                };
                    var d = {"ab":"","km":{},"pv":"","vu":"a6115f6b-32e0-4ca2-4b50-0e210d02383e","r":{"r":[{"p":" ezoic_pub_ad_placeholder-676-sidebar_middle-160x600-676-nonexxxnonexxxxxxezmaxscaleval100 ","s":676,"h":600,"w":160,"r":true},{"p":" ezoic_pub_ad_placeholder-100-bottom_floating-970x90-100-nonexxxnonexxxxxxezmaxscaleval100 ","s":100,"h":90,"w":970,"r":true}],"a":{"100":true,"12":true,"6":true,"676":true},"g":-1,"l":{"0":2,"1":3,"2":2,"3":1,"4":-1,"5":-1},"m":{"0":2,"1":3,"2":2,"3":1,"4":-1,"5":-1},"v":0,"ve":false,"hr":false,"sa":false},"cr":"","tid":134,"tn":"pub_site","url":"","wc":32,"ff":1,"dhh":""};
                    d.ab = _ezaq.ab_test_id;
                    d.pv = _ezaq.page_view_id;
                    d.vu = _ezaq.visit_uuid;
                    d.url = window.location.href;
                    var dynamicAddScript = function(script) {
                        if(window.ezFinishedStatic === true) {
                            console.error("attempted to load dynamic script again");
                            return;
                        }
                        var errorMessages = [];
                        function errorHandler(event) {
                          var errorObj = event.error;
                          if (errorObj && errorObj.stack && errorObj.stack.indexOf('dynamicAddScript') !== -1) {
                            var errorMessage = {
                              Message: event.message,
                              LineNo: event.lineno,
                              ColumnNo: event.colno,
                              Stack: errorObj.stack
                            };
                            errorMessages.push(errorMessage);
                          }
                    
                          if (typeof window.onerror === 'function') {
                            window.onerror.apply(this, arguments);
                          }
                        }
                         window.addEventListener('error', errorHandler);
                    
                        var ezDynamic = document.createElement('script');
                        ezDynamic.type = 'text/javascript';
                        ezDynamic.innerHTML = script;
                        document.head.appendChild(ezDynamic);
                    
                        window.removeEventListener('error', errorHandler);
                        if (window.ezFinishedStatic !== true || typeof window.ezstaticerrors !== 'undefined') {
                            d.Script = script;
                            d.ErrorMessages = JSON.stringify(errorMessages);
                            d.ErrorStaticMessages = window.ezstaticerrors || '';
                            var dataTxt = JSON.stringify(d);
                            if (dataTxt.length > 0) {
                                var logXHR = new XMLHttpRequest();
                                logXHR.open('POST','/ezais/log?cb=1', true);
                                logXHR.setRequestHeader('Content-Type','application/json');
                                logXHR.send(dataTxt);
                            }
                        }
                    };
                    var dataText = JSON.stringify(d);
                    if (dataText.length > 0) {
                        var startTime = Date.now() - __ez.stms;
                    
                        var xhr = new XMLHttpRequest();
                        xhr.open('POST','/ezais/dynamic?cb=1', true);
                        xhr.onload = function () {
                            if (xhr.status!=200) {
                                return;
                            }
                    
                            if(document.readyState !== 'loading') {
                                dynamicAddScript(xhr.response);
                                return;
                            }
                    
                            var eventFunc = function() {
                                if(document.readyState === 'loading') {
                                    return;
                                }
                                document.removeEventListener('readystatechange', eventFunc, false);
                                dynamicAddScript(xhr.response);
                            };
                    
                            document.addEventListener('readystatechange', eventFunc, false);
                        };
                        xhr.setRequestHeader('Content-Type','text/plain');
                        xhr.send(dataText);
                    }
                    }
                    
                    function callDynamicAfterConsent() {
                        if (window.__tcfapi) {
                            window.__tcfapi("addEventListener", 2, function (tcdata, success) {
                                if (success) {
                                    if(tcdata.gdprApplies == true) {
                                        if(tcdata.eventStatus === "useractioncomplete" || tcdata.eventStatus === "tcloaded") {
                                            __ez.queue.addFunc("productEzoicAds", "productEzoicAds", null, true, ['ezaqReady'], false, false, false, true);
                                        }
                                    } else {
                                        __ez.queue.addFunc("productEzoicAds", "productEzoicAds", null, true, ['ezaqReady'], false, false, false, true);
                                    }
                                }
                            });
                        } else {
                            __ez.queue.addFunc("productEzoicAds", "productEzoicAds", null, true, ['ezaqReady'], false, false, false, true);
                        }
                    }
                    
                    
                    if (window.__tcfapi) {
                        callDynamicAfterConsent();
                    } else {
                        if (typeof window.ezCMPQueue !== "undefined") {
                            window.ezCMPQueue.push(callDynamicAfterConsent);
                        } else {
                            __ez.queue.addFunc("productEzoicAds", "productEzoicAds", null, true, ['ezaqReady'], false, false, false, true);
                        }
                    }
                    </script><base href="https://editor.plantuml.com/uml/">
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">    
                        <link rel="shortcut icon" href="https://plantuml.com/favicon.ico">
                        <title>PlantUML Editor</title>
                        <style>
                            html {
                                margin: 0;
                                padding: 0;
                                height: 100%;
                            }
                            body {
                                margin: 0 0 0 170px;
                                padding: 0;
                                height: 100%;
                                overflow: hidden;
                            }
                            a {
                                color: #007bff;
                                text-decoration: none;
                            }
                            a:hover, a:focus {
                                color: #0056b3;
                                text-decoration: underline;
                            }
                    
                    #header {
                        background-color: #f8f7e3;
                        border: 2px solid #d6d1ab;
                        padding: 10px;
                        margin: 10px;
                        text-align: center;
                        border-radius: 8px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    }
                    
                            #footer {
                                margin: 0 0 0 30px;
                                padding: 10px;
                            }
                    
                        #external_container {
                               display: flex;
                               flex-direction: column;
                              height: calc(100vh - 120px);
                            overflow: hidden;
                        }
                    
                            #container {
                                display: flex;
                                flex: 1;
                                min-height: 300px;
                                height: 300px;
                                width: 100%;
                                overflow: hidden;
                            }
                    
                            #editor {
                                flex-grow: 0;
                                flex-shrink: 0;
                                width: calc(50% - 160px);
                                min-width: 200px;
                            }
                    
                            #resizer {
                                width: 5px;
                                background-color: #f1f1f1;
                                cursor: col-resize;
                            }
                    
                            #image-container {
                                flex-grow: 100;
                                flex-shrink: 100;
                                padding: 10px;
                                overflow: auto;
                            }
                    
                            #cola, #colb {
                                flex-grow: 0;
                                flex-shrink: 1;
                            }
                    
                            #image-container img {
                                max-width: 100%;
                                height: auto;
                            }
                    
                    
                        #side-panel {
                            position: absolute;
                            top: 0;
                            left: 3px;
                            width: 165px;
                            height: 100%;
                        }
                    
                        </style>
                    
                        <style>
                            table {
                                width: 100%;
                                margin: 1px auto;
                                border-collapse: collapse;
                                border: 1px solid #ddd;
                                border-radius: 5px;
                                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                            }
                            th, td {
                                padding: 1px 5px;
                                text-align: left;
                                border-bottom: 1px solid #ddd;
                                cursor: pointer;
                            }
                            tbody tr:hover {
                                background-color: #f1f1f1;
                            }
                            table button {
                                padding: 1px;
                                background-color: #f6f8fa; /* Lighter grey */
                                color: #24292e; /* Dark text color */
                                border: 1px solid #d1d5da; /* Light grey border */
                                border-radius: 6px;
                                cursor: pointer;
                                margin: 0;
                            }
                            table button:hover {
                                background-color: #e1e4e8; /* Slightly darker on hover */
                            }
                        </style>
                    
                        <style>
                            /* Style for the text area */
                            #encoded-input {
                                width: 100%;
                                padding: 2px;
                                font-size: 14px;
                                border: 1px solid #ccc;
                                border-radius: 4px;
                                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
                            }
                    
                            /* Apply the flex layout to the container */
                            .tooltip-container {
                                display: flex;
                                align-items: center; /* Vertically align the elements */
                                position: relative;
                            }
                    
                            /* Style for the input */
                            #encoded-input {
                                flex-grow: 1; /* Allow the input to take up as much space as possible */
                                margin-right: 10px; /* Space between the encoded input and the Decode button */
                            }
                    
                            /* Style for the Decode button */
                            .decode-btn, .btn {
                                padding: 4px 8px;
                                background-color: #f6f8fa; /* Lighter grey */
                                color: #24292e; /* Dark text color */
                                border: 1px solid #d1d5da; /* Light grey border */
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                            }
                    
                            .decode-btn {
                                white-space: nowrap;
                            }
                    
                            /* Style for the copy button */
                            .btn {
                                margin: 10px 0; /* Adds some spacing above and below */
                            }
                    
                            .btn:hover,.decode-btn:hover {
                                background-color: #e1e4e8; /* Slightly darker on hover */
                            }
                    
                            /* Style for the delete button */
                            .delete-btn {
                                padding: 2px 8px;
                                background-color: #f6f8fa; /* Lighter grey */
                                color: #24292e; /* Dark text color */
                                border: 1px solid #d1d5da; /* Light grey border */
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                                margin: 2px 0; /* Adds some spacing above and below */
                            }
                    
                            .delete-btn:hover {
                                background-color: #e1e4e8; /* Slightly darker on hover */
                            }
                    
                        </style>
                    
                        <style>
                            /* Styles for the background overlay */
                            #back-div {
                                position: fixed;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background-color: rgba(0, 0, 0, 0.5); /* Transparent dark background */
                                z-index: 999; /* Back div behind load_div */
                            }
                    
                            /* Styles for the file loader div */
                            #load-div {
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                background-color: white; /* Light background */
                                border: 1px solid #ccc;
                                padding: 20px; /* Increase padding for a larger look */
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                                width: 70%;  /* Increased width */
                                height: 60%; /* Increased height */
                                overflow-y: auto; /* Add scroll if content overflows vertically */
                                z-index: 1000; /* Ensure the div is in the foreground */
                            }
                    
                            /* Style for the delete button */
                            .delete-btn {
                                cursor: pointer;
                            }
                        </style>
                    
                    
                        <style>
                            /* Dark Mode Styles */
                            body.dark-mode {
                                background-color: #1b1b1b;
                                color: #ffffff;
                            }
                    
                            body.dark-mode a {
                                color: #48a9e0;
                            }
                            body.dark-mode a:hover, body.dark-mode a:focus {
                                color: #76c7f4;
                            }
                    
                            body.dark-mode #header {
                                background-color: #313139;
                            }
                            body.dark-mode #resizer {
                                background-color: #313139;
                            }
                    
                            body.dark-mode #footer {
                                background-color: #1e1e1e;
                            }
                    
                            body.dark-mode table {
                                background-color: #1e1e1e;
                                color: #ffffff;
                                border: 1px solid #444;
                            }
                    
                            body.dark-mode table button {
                                background-color: #444;
                                color: #ffffff;
                            }
                    
                            body.dark-mode tbody tr:hover {
                                background-color: #333;
                            }
                    
                            body.dark-mode #encoded-input {
                                background-color: #333;
                                color: #ffffff;
                                border: 1px solid #444;
                            }
                    
                            body.dark-mode .decode-btn,body.dark-mode .btn {
                                background-color: #444;
                                color: #ffffff;
                                border: 1px solid #444;
                            }
                            body.dark-mode .decode-btn:hover, body.dark-mode .btn:hover {
                                background-color: #777;
                            }
                    
                            /* Dark mode styles for back-div and load-div */
                            body.dark-mode #back-div {
                                background-color: rgba(0, 0, 0, 0.7); /* Slightly darker transparent background for dark mode */
                            }
                    
                            body.dark-mode #load-div {
                                background-color: #2a2a2a; /* Dark background for the load div */
                                border: 1px solid #555; /* Darker border for the load div */
                                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5); /* A more subtle shadow for dark mode */
                            }
                        </style>
                    
                        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.14/ace.js"></script>
                        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.14/ext-language_tools.js"></script>
                        <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.0.4/pako.min.js"></script>
                        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/brython@3.12.2/brython.js"></script>
                        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/brython@3.12.2/brython_stdlib.js"></script>
                        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
                        <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
                    <script type="text/javascript">
                    var ezoTemplate = 'pub_site';
                    var ezouid = '1';
                    var ezoFormfactor = '1';
                    </script><script data-ezscrex="false" type="text/javascript">
                    var soc_app_id = '0';
                    var did = 173770;
                    var ezdomain = 'plantuml.com';
                    var ezoicSearchable = 1;
                    </script>
                    <script async="" data-ezscrex="false" data-cfasync="false" src="//www.humix.com/video.js"></script><script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.14/theme-github.js"></script><script src="https://go.ezodn.com/detroitchicago/indy.js?cb=37&amp;gcb=0" async=""></script><script src="//www.ezojs.com/parsonsmaize/mulvane.js?gcb=195-2&amp;cb=e75e48eec0" async=""></script><script src="//www.ezojs.com/detroitchicago/birmingham.js?gcb=195-2&amp;cb=539c47377c" async=""></script><script type="text/javascript">if(typeof _setEzCookies!=='function'){function _setEzCookies(ezConsentData){var cookies=window.ezCookieQueue;for(var i=0;i<cookies.length;i++){var cookie=cookies[i];if(ezConsentData&&ezConsentData.loaded&&ezConsentData[cookie.tcfCategory]){document.cookie=cookie.name+"="+cookie.value;}}}}
                    window.ezCookieQueue=window.ezCookieQueue||[];if(typeof addEzCookies!=='function'){function addEzCookies(arr){window.ezCookieQueue=[...window.ezCookieQueue,...arr];}}
                    addEzCookies([{name:"lp_173770",value:"https://plantuml.com/fr/; Path=/; Domain=plantuml.com; Expires=Sat, 31 May 2025 12:56:22 UTC",tcfCategory:"store_info",isEzoic:"true",},{name:"ezovuuidtime_173770",value:"1748694382; Path=/; Domain=plantuml.com; Expires=Mon, 02 Jun 2025 12:26:22 UTC",tcfCategory:"understand_audiences",isEzoic:"true",},{name:"ezovuuid_173770",value:"a6115f6b-32e0-4ca2-4b50-0e210d02383e; Path=/; Domain=plantuml.com; Expires=Sat, 31 May 2025 12:56:22 UTC",tcfCategory:"understand_audiences",isEzoic:"true",},{name:"ezoref_173770",value:"plantuml.com; Path=/; Domain=plantuml.com; Expires=Sat, 31 May 2025 12:56:22 UTC",tcfCategory:"understand_audiences",isEzoic:"true",}]);if(window.ezTcfConsent&&window.ezTcfConsent.loaded){_setEzCookies(window.ezTcfConsent);}else if(typeof getEzConsentData==="function"){getEzConsentData().then(function(ezTcfConsent){if(ezTcfConsent&&ezTcfConsent.loaded){_setEzCookies(window.ezTcfConsent);}else{console.error("cannot get ez consent data");_setEzCookies(window.ezTcfConsent);}});}else{console.error("getEzConsentData is not a function");_setEzCookies(window.ezTcfConsent);}window._ezaq = Object.assign({"ab_test_id":"mod275-c","ad_cache_level":1,"ad_count_adjustment":0,"ad_lazyload_version":0,"ad_load_version":1,"ad_location_ids":"","adpicker_placement_cnt":0,"adx_ad_count":0,"ai_placeholder_cache_level":1,"ai_placeholder_placement_cnt":-1,"bidder_method":0,"bidder_version":3,"city":"Moknine","country":"TN","days_since_last_visit":-1,"display_ad_count":0,"domain":"plantuml.com","domain_id":173770,"domain_test_group":20230802,"edge_cache_status":31,"edge_response_time":880,"engaged_time_visit":654,"ezcache_level":0,"ezcache_skip_code":14,"first_party_signals_domain_categories":"596","form_factor_id":1,"framework_id":1,"full_url":"https://editor.plantuml.com/uml/","has_bad_image":0,"has_bad_words":0,"iab_category":"","is_embed":false,"is_from_recommended_pages":false,"is_return_visitor":false,"is_sitespeed":1,"last_page_load":"1748691805097","last_pageview_id":"56cc7f53-25d1-4c10-42f4-e20ccfe5ce32","lt_cache_level":0,"max_ads":0,"metro_code":0,"optimization_version":0,"ortb_signals_language":"en","page_ad_positions":"","page_view_count":0,"page_view_id":"aec61d74-cf35-4cd3-52ae-8020a89f8cf3","position_selection_id":0,"postal_code":"","product_1":true,"product_3":true,"product_5":true,"product_6":true,"pv_event_count":0,"referring_domain":"plantuml.com","response_size":42683,"response_size_orig":32051,"response_time_orig":991,"serverid":"i-050df12d4ffde83c7","site_iab_categories":[632,1019,132],"state":"52","sub_page_ad_positions":"","t_epoch":1748694382,"template_id":134,"time_on_site_visit":5640,"url":"https://editor.plantuml.com/uml/","visit_uuid":"a6115f6b-32e0-4ca2-4b50-0e210d02383e","weather_precipitation":0,"weather_summary":"","weather_temperature":0,"word_count":32,"worst_bad_word_level":0}, typeof window._ezaq !== "undefined" ? window._ezaq : {});__ez.queue.markLoaded('ezaqReady');
                    __ez.queue.addFile('/parsonsmaize/mulvane.js', '/parsonsmaize/mulvane.js?gcb=195-2&cb=e75e48eec0', true, ['/parsonsmaize/abilene.js'], true, false, true, false);__ez.queue.addFile('/parsonsmaize/olathe.js', '/parsonsmaize/olathe.js?gcb=195-2&cb=26', false, ['/parsonsmaize/abilene.js','/parsonsmaize/mulvane.js'], true, false, true, false);__ez.queue.addFile('/porpoiseant/et.js', '/porpoiseant/et.js?gcb=195-2&cb=3', false, [], true, false, true, false);__ez.queue.addFile('/detroitchicago/reno.js', '/detroitchicago/reno.js?gcb=195-2&cb=3', false, ['/parsonsmaize/abilene.js'], true, false, true, false);__ez.queue.addFile('/detroitchicago/overlandpark.js', '/detroitchicago/overlandpark.js?gcb=195-2&cb=301bbdaf04', false, ['/parsonsmaize/abilene.js'], true, false, true, false);__ez.queue.addFile('/detroitchicago/birmingham.js', '/detroitchicago/birmingham.js?gcb=195-2&cb=539c47377c', true, ['/parsonsmaize/abilene.js'], true, false, true, false);
                    __ez.queue.addFile('/detroitchicago/wichita.js', '/detroitchicago/wichita.js?gcb=195-2&cb=9f9286e31b', false, ['/parsonsmaize/abilene.js'], true, false, true, false);__ez.queue.addFile('/detroitchicago/raleigh.js', '/detroitchicago/raleigh.js?gcb=195-2&cb=8', false, ['/parsonsmaize/abilene.js'], true, false, true, false);__ez.queue.addFile('/detroitchicago/vista.js', '/detroitchicago/vista.js?gcb=195-2&cb=296945a885', false, ['/parsonsmaize/abilene.js'], true, false, true, false);
                    function create_ezolpl() {
                        var d = new Date();
                        d.setTime(d.getTime() + 365 * 24 * 60 * 60 * 1000);
                        var expires = "expires=" + d.toUTCString();
                        __ez.ck.setByCat(
                          "ezux_lpl_173770",
                          new Date().getTime() +
                            "|" +
                            _ezaq.page_view_id +
                            "|" +
                            _ezaq.is_return_visitor +
                            "; " +
                            expires,
                          "understand_audiences",
                          false
                        );
                      }
                      function attach_ezolpl() {
                        if (document.readyState === "complete") {
                          create_ezolpl();
                          return;
                        }
                        window.addEventListener("load", create_ezolpl);
                      }  
                    
                    __ez.queue.addFunc("attach_ezolpl", "attach_ezolpl", null, false, ['/detroitchicago/boise.js'], true, false, false, false);
                    
                    __ez.queue.addFile('/tardisrocinante/vitals.js', '/tardisrocinante/vitals.js?gcb=2&cb=5', false, ['/parsonsmaize/mulvane.js'], true, false, true, false);
                    var _audins_dom="plantuml_com",_audins_did=173770;__ez.queue.addDelayFunc("audins.js","__ez.script.add", "//go.ezodn.com/detroitchicago/audins.js?cb=3");
                    __ez.queue.addFile('/beardeddragon/drake.js', '/beardeddragon/drake.js?gcb=2&cb=1b0a0a9dcc', false, [], true, false, true, false);
                    var __ez_dims = (function() {
                            var setCookie = function( name, content, expiry ) {
                                return document.cookie = name+'='+content+((expiry)?';expires='+(new Date(Math.floor(new Date().getTime()+expiry*1000)).toUTCString()):'')+';path=/';
                            };
                            var ffid = 1;
                            var oh = window.screen.height;
                            var ow = window.screen.width;
                            var h = ffid === 1 ? oh : (oh > ow) ? oh : ow;
                            var w = ffid === 1 ? ow : (oh > ow) ? ow : oh;
                            var uh = window.innerHeight || document.documentElement.clientHeight || document.getElementsByTagName('body')[0].clientHeight;
                            var uw = window.innerWidth || document.documentElement.clientWidth || document.getElementsByTagName('body')[0].clientWidth;
                        
                            var setAllCookies = function() {
                                setCookie('ezds', encodeURIComponent('ffid='+ffid+',w='+w+',h='+h), (31536e3*7));
                                setCookie('ezohw', encodeURIComponent('w='+uw+',h='+uh), (31536e3*7));    
                            };
                        
                            if (window.ezTcfConsent && window.ezTcfConsent.loaded) {
                                if (window.ezTcfConsent.understand_audiences) { 
                                    setAllCookies();
                                }
                            } else if (typeof getEzConsentData === "function") {
                                getEzConsentData().then(function (ezTcfConsent) {
                                    if (ezTcfConsent && ezTcfConsent.loaded) {
                                        if (ezTcfConsent.understand_audiences) { 
                                            setAllCookies();
                                        }
                                    } else {
                                        console.error("cannot get ez consent data");
                                        setAllCookies();
                                    }
                                });
                            } else {
                                console.error("getEzConsentData is not a function");
                                setAllCookies();
                            }
                        
                        })();
                    __ez.queue.addFile('/parsonsmaize/chanute.js', '/parsonsmaize/chanute.js?a=a&cb=16&dcb=195-2&shcb=34', true, ['/parsonsmaize/mulvane.js'], true, false, false, false);
                    __ez.queue.addFile('/porpoiseant/jellyfish.js', '/porpoiseant/jellyfish.js?a=a&cb=17&dcb=195-2&shcb=34', false, [], true, false, false, false);
                    if(typeof _ezaq!=="undefined"&&typeof __ez=="object"&&typeof __ez.bit=="object"&&typeof __ezDotData=="function"){if("cookieDeprecationLabel"in navigator){navigator.cookieDeprecationLabel.getValue().then((label)=>{__ez.bit.Add(_ezaq["page_view_id"],[new __ezDotData("chrome_cookie_deprecation_label",label),]);});}}
                    </script><script src="//www.ezojs.com/parsonsmaize/mulvane.js?gcb=195-2&amp;cb=e75e48eec0" async=""></script><script src="//www.ezojs.com/porpoiseant/et.js?gcb=195-2&amp;cb=3" async=""></script><script src="//www.ezojs.com/detroitchicago/reno.js?gcb=195-2&amp;cb=3" async=""></script><script src="//www.ezojs.com/detroitchicago/overlandpark.js?gcb=195-2&amp;cb=301bbdaf04" async=""></script><script src="//www.ezojs.com/detroitchicago/birmingham.js?gcb=195-2&amp;cb=539c47377c" async=""></script><script src="//www.ezojs.com/detroitchicago/wichita.js?gcb=195-2&amp;cb=9f9286e31b" async=""></script><script src="//www.ezojs.com/detroitchicago/raleigh.js?gcb=195-2&amp;cb=8" async=""></script><script src="//www.ezojs.com/detroitchicago/vista.js?gcb=195-2&amp;cb=296945a885" async=""></script><script src="//www.ezojs.com/beardeddragon/drake.js?gcb=2&amp;cb=1b0a0a9dcc" async=""></script><script src="//www.ezojs.com/porpoiseant/jellyfish.js?a=a&amp;cb=17&amp;dcb=195-2&amp;shcb=34" async=""></script><script src="//www.ezojs.com/parsonsmaize/olathe.js?gcb=195-2&amp;cb=26" async=""></script><script src="//www.ezojs.com/tardisrocinante/vitals.js?gcb=2&amp;cb=5" async=""></script><script src="//www.ezojs.com/parsonsmaize/chanute.js?a=a&amp;cb=16&amp;dcb=195-2&amp;shcb=34" async=""></script><script type="text/javascript">var ezStaticAnchor = function(anchorXPath, htmlStr, isRequired) {
                        var ezRange = document.createRange();
                        var ezAnchor = document.evaluate(anchorXPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                        if (typeof ezAnchor !== 'undefined' && ezAnchor !== null) {
                            ezRange.selectNode(ezAnchor);
                            var fragment = ezRange.createContextualFragment(htmlStr);
                            if(isRequired===true) {
                                ezAnchor.appendChild(fragment);
                            } else {
                                ezAnchor.parentNode.insertBefore(fragment, ezAnchor.nextSibling);
                            }
                        } else {
                            var errMsg = 'cannot find anchor for ad position' + anchorXPath + ';';
                            console.error(errMsg);
                            window.ezstaticerrors = (window.ezstaticerrors || '') + errMsg;
                        }
                    };
                    try {window.ezslots_raw=[];window.ezslotdivs={};var __sellerid='1dbb067e4138c8d0cfe36f9c3b751a1f';var __ez_nid ='***********';
                    
                    
                    __ez.jitver=1;
                    
                    !function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function t(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}window.ezasLoaded=!1;var i=!1;window.ezasBuild=function(e){window.ezasLoaded||(void 0===window.ezasAutoAds&&(window.google_reactive_ads_global_state={adCount:{},floatingAdsStacking:{maxZIndexListeners:[],maxZIndexRestrictions:{},nextRestrictionId:0},messageValidationEnabled:!1,reactiveTypeDisabledByPublisher:{},reactiveTypeEnabledInAsfe:{},sideRailAvailableSpace:[],sideRailOverlappableElements:[],stateForType:{},tagSpecificState:{},wasPlaTagProcessed:!0,wasReactiveAdConfigReceived:{1:!0,2:!0,8:!0},wasReactiveAdVisible:{},wasReactiveTagRequestSent:!0,description:"Can't disable auto ads programmatically on the page, so here we are!"}),window.ezasLoaded=!0);var t=new o(e);if(1!=t.getValue("compid"))return!1;if("function"!=typeof MutationObserver||"function"!=typeof IntersectionObserver)return t.setValue("compid","0"),!1;if(void 0===e||void 0===window.ezasVars)return t.setValue("compid","0"),!1;if(void 0!==window.ezgconsent&&0==window.ezgconsent)return t.setValue("compid","0"),!1;if(!i&&void 0===window.ezasAutoAds){var a=document.createElement("script");a.src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",a.crossOrigin="anonymous",a.async=!0,document.getElementsByTagName("head")[0].appendChild(a),i=!0}if(5==t.getValue("al")%1e3)return t.setValue("compid","0"),t.setValue("nocompoverride","1"),!1;var n=window.ezasVars.cid,d="ca-"+window.ezasVars.pid;if(void 0===n||""===n)return t.setValue("compid","0"),!1;t.setValue("reft","n");var s=document.getElementById(e);if(!s)return t.setValue("compid","0"),!1;var l=e+"-asloaded";if(null!==document.getElementById(l))return!0;var r=document.createElement("ins");r.id=l,r.className="adsbygoogle ezasloaded",r.dataset.adClient=d,r.dataset.adChannel=n;var w=t.getValue("asau");return"mod105"==t.getValue("bra")&&""!=w?(r.dataset.adSlot=w,r.dataset.matchedContentUiType="text",r.dataset.matchedContentRowsNum="4",r.dataset.matchedContentColumnsNum="1"):void 0!==window.__ezasAggressive&&!0===window.__ezasAggressive&&(r.dataset.fullWidthResponsive="true"),r.style.display="block",r.style.margin="0px auto","undefined"!=typeof handleResponsiveAdsense?window.handleResponsiveAdsense(r,s):(r.style.width=s.attributes.ezaw.value+"px",r.style.height=s.attributes.ezah.value+"px"),s.appendChild(r),window.ezaslWatch=window.ezaslWatch||[],window.ezaslWatch.push(e),window.__ez&&__ez.fads&&__ez.fads.log("ezasbuild firing adsense for slot",e),(window.adsbygoogle=window.adsbygoogle||[]).push({}),ezoSTPixelAdd(e,"stat_source_id",44),ezoSTPixelAdd(e,"adsensetype",1),!0};var o=function(){function i(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),t(this,"slot",null),t(this,"allValues",null),t(this,"isEzSlot",!1),t(this,"divID",""),this.divID=e;var o=window.ezslotdivs&&window.ezslotdivs[e]&&window.ezslotdivs[e].slot;if(void 0===window[o+"_raw"]){if(!ezslots||0==ezslots.length)return null;var a=ezslots.filter((function(t){return window[t].getSlotElementId()===e}));if(0==a.length)return null;console.debug("as2"),this.slot=window[a[0]],this.allValues=window[a[0]].getTargetingMap(),this.isEzSlot=!0}else this.allValues=window[o+"_raw"]}var o,a;return o=i,(a=[{key:"getValue",value:function(e){return this.isEzSlot&&this.slot?this.slot.getTargeting(e)[0]:this.allValues[e]}},{key:"setValue",value:function(e,t){this.isEzSlot&&this.slot?this.slot.setTargeting(e,t):this.allValues[e]=t}},{key:"getSlot",value:function(){if(this.slot)return this.slot;var e=window.ezslotdivs&&window.ezslotdivs[this.divID]&&window.ezslotdivs[this.divID].adunit;if(!e){e="";var t=this.divID.match(/div-gpt-ad-([a-z0-9_-]+)-0(_[0-9])?/);t&&t.length>1&&(e=t[1])}return{ElementId:this.divID,Targeting:this.allValues,AdUnitPath:e}}}])&&e(o.prototype,a),Object.defineProperty(o,"prototype",{writable:!1}),i}();window.ezasvEvent=function(e,t){if(!0===e[0].isIntersecting){var i=e[0].target.attributes[0].value.substr(0,e[0].target.attributes[0].value.length-9);window.ezoSTPixelAdd(i,"viewed",1),t.disconnect()}},window.ezaslEvent=function(e,t){if(window.__ez&&__ez.fads&&__ez.fads.log("ezaslEvent slot event triggered"),void 0!==e[0].target.attributes["data-ad-status"]){var i=e[0].target.attributes["data-ad-status"].value,o=e[0].target.attributes[0].value.substr(0,e[0].target.attributes[0].value.length-9);if(window.__ez&&__ez.fads&&__ez.fads.log("ezaslEvent slot event found ad",o,i),"filled"==i)return window.ezoSTPixelAdd(o,"loaded",1),window.ezoSTPixelAdd(o,"ad_load_time",Date.now()-__ez.stms),new IntersectionObserver(window.ezasvEvent,{threshold:[1]}).observe(document.getElementById(o+"-asloaded")),void t.disconnect();if("unfilled"==i){var a=ezslots_raw.filter((function(t){return t.tap&&t.tap.startsWith(e[0].target.parentElement.id.slice(11,-2))}));if(a.length>0&&"1"==a[0].nocompoverride)return;return setTimeout((function(){window.ezoAdBackFill(e)}),2e3),void t.disconnect()}}},window.ezoAdBackFill=function(e){var t=e[0].target,i=t.parentElement;t.style.display="none !important",window.__ez&&__ez.fads&&__ez.fads.log("ezoAdBackFill attempting to backfill adsense with GAM",t,t.id);var o,a=ezoGetSlotNum(i.id);if(o=void 0!==__ez.fads?__ez.fads.initslots[i.id]:__ez_fad_initslot[i.id],void 0===a&&"function"==typeof o&&(a=o(6)),void 0!==a)var n=window[a];if(void 0!==n){n.setTargeting("compid","0"),n.setTargeting("nocompoverride","1"),n.setTargeting("bkfl","1"),n.setTargeting("reft","t");var d=n.getTargeting("br2")[0];if(void 0!==window.ezoibfh&&void 0!==window.ezoibfh[d]&&(n.setTargeting("br1",d),n.setTargeting("eb_br",window.ezoibfh[d])),googletag.display(n),void 0!==window.ezoResponsiveSizes&&void 0!==n&&null!=n&&"1005"!=n.getTargeting("al")[0]&&"3005"!=n.getTargeting("al")[0]){var s=__ez.fads.adLoadGAM.buildSlotResponsiveSizes(n.getSlotElementId());""!==s&&(__ez.fads.adLoadGAM.adjustResponsiveDiv(n.getSlotElementId()),n.defineSizeMapping(s))}setTimeout((function(){googletag.pubads().refresh([n])}),500)}},window.ezaslWatch=window.ezaslWatch||[],window.ezaslWatch.push=function(e){var t=e+"-asloaded";window.__ez&&__ez.fads&&__ez.fads.log("attached observer to slot",t),new MutationObserver(window.ezaslEvent).observe(document.getElementById(t),{attributes:!0});var i=document.getElementById(t);i.hasAttribute("data-ad-status")&&(i.setAttribute("data-ezasl","1"),i.removeAttribute("data-ezasl"))};for(var a=0;a<ezaslWatch.length;a++){var n=new MutationObserver(window.ezaslEvent);n.observe(document.getElementById(ezaslWatch[a]+"-asloaded"),{attributes:!0}),window.ezaslEvent([{target:document.getElementById(ezaslWatch[a]+"-asloaded")}],n)}window.ezoSTPixels=window.ezoSTPixels||[];var d=setInterval((function(){"undefined"!=typeof __ez&&"undefined"!=typeof __ezDotData&&"undefined"!=typeof ezslots&&"undefined"!=typeof ezslots_raw&&window.ezslots_raw.length>0&&(window.ezoSTPixelFire(),clearInterval(d))}),250);window.ezoSTPixelAdd=function(e,t,i){window.ezoSTPixels.push({id:e,name:t,value:i}),window.ezoSTPixelFire()},window.ezoGetSlotById=function(e){var t;for(var i in t=e.includes("/")?e.split("div-gpt-ad-")[1].split("-")[0]:e.split("div-gpt-ad-")[1].split("-",3).join("-"),window.ezslots_raw)if(window.ezslots_raw[i].tap.includes(t))return window.ezslots_raw[i]},window.ezoGetSlotNum=function(e){if(void 0!==window.ezslots&&0!=window.ezslots)for(var t=0;t<window.ezslots.length;t++){var i=window[ezslots[t]];if(void 0!==i){var o=i.getSlotElementId();if(void 0!==o&&o==e)return ezslots[t]}}},window.ezoSTPixelFire=function(){if("undefined"!=typeof __ez&&"undefined"!=typeof __ezDotData&&"undefined"!=typeof ezslots)for(;window.ezoSTPixels.length>0;){var e=window.ezoSTPixels.shift(),t=window.ezoGetSlotById(e.id);if(void 0===t){var i;if(i=void 0!==__ez.fads?__ez.fads.initslots[e.id]:__ez_fad_initslot[e.id],void 0===googletag.defineSlot)return void window.ezoSTPixels.push(e);if(i(1),void 0===(t=window.ezoGetSlotById(e.id)))return void window.ezoSTPixels.push(e)}var o=[{type:"impression",impression_id:t.eid,domain_id:window.did.toString(),unit:e.id,t_epoch:__ez.dot.getEpoch(0),ad_position:parseInt(t.ap),country_code:__ez.dot.getCC(),pageview_id:__ez.dot.getPageviewId(),comp_id:1,data:__ez.dot.dataToStr([new __ezDotData(e.name,e.value.toString())]),is_orig:0}],a=__ez.dot.getURL("/porpoiseant/army.gif")+"?orig=0&sts="+btoa(JSON.stringify(o));__ez.dot.Fire(a)}}}();
                    
                    
                        window.__ezaps=[{"slotID":"div-gpt-ad-plantuml_com-large-billboard-2-0","divID":"","slotName":"/***********,22569517314/plantuml_com-large-billboard-2","sizes":[[160,600]],"slotParams":{"gpid":"/***********,22569517314/plantuml_com-large-billboard-2"}},{"slotID":"div-gpt-ad-plantuml_com-medrectangle-2-0","divID":"","slotName":"/***********,22569517314/plantuml_com-medrectangle-2","sizes":[[970,90],[728,90]],"slotParams":{"gpid":"/***********,22569517314/plantuml_com-medrectangle-2"}},{"slotID":"div-gpt-ad-plantuml_com-square-1-0","divID":"","slotName":"/***********,22569517314/plantuml_com-square-1","sizes":[[1,1]],"slotParams":{"gpid":"/***********,22569517314/plantuml_com-square-1"}}];window.__ezapsVideo=[];window.__ezapid='aa05931b-5308-4ea3-95a2-adf84f4ffde4';
                        (function() {
                        var s = document.createElement('script');
                        s.setAttribute('src', '/edmontonalberta/calgary.js?cb=3a909d4c24');
                        s.setAttribute('data-ezscrex', 'false');
                        s.setAttribute('async', 'true');
                        s.setAttribute('data-pagespeed-no-defer', 'true');
                        s.setAttribute('type', 'text/javascript');
                        document.body.appendChild(s);
                        })();
                    
                    
                    !function(){var e=setInterval((function(){var t=document.querySelectorAll('[id^="outbrain_widget_"]');0!==t.length&&(function(e){e.forEach((function(e){if(e.parentElement.hasAttribute("data-ez-name")){var t=e.parentElement.attributes["data-ez-name"].value;new IntersectionObserver((function(e,i){e[0].isIntersecting&&(n({unit:t,name:"viewed"}),i.disconnect())}),{threshold:[1]}).observe(e);var i=e.attributes["data-widget-id"].value;window.OBREvents=window.OBREvents||[],OBREvents.push({event:"widgetDataReturned",widgetId:i,func:function(){n({unit:t,name:"loaded"})}})}}))}(t),clearInterval(e))}),250),t=[],i=setInterval((function(){"undefined"!=typeof __ez&&"undefined"!=typeof _ezim_d&&"undefined"!=typeof __ezDotData&&(d(),clearInterval(i))}),2500);function n(e){t.push(e),d()}function d(){var e,i;if(0!==t.length&&"undefined"!=typeof __ez&&"undefined"!=typeof _ezim_d&&"undefined"!=typeof __ezDotData)for(;t.length>0;){var n=t.shift(),d=(e=n.unit,i=n.name,[{type:"impression",impression_id:_ezim_d[e].full_id.split("/")[2],domain_id:window.did.toString(),unit:e,t_epoch:__ez.dot.getEpoch(0),ad_position:_ezim_d[e].position_id,country_code:__ez.dot.getCC(),pageview_id:__ez.dot.getPageviewId(),comp_id:2,data:__ez.dot.dataToStr([new __ezDotData(i,"1")]),is_orig:0}]),o=__ez.dot.getURL("/porpoiseant/army.gif")+"?orig="+(!0===__ez.template.isOrig?1:0)+"&sts="+btoa(JSON.stringify(d));void 0!==window.ezWp&&ezWp&&void 0!==window._ezaq&&_ezaq.hasOwnProperty("visit_uuid")&&(o+="&visit_uuid="+window.visit_uuid),__ez.dot.Fire(o)}}}();
                    
                    
                    
                    
                    window.ezhbopt=true;
                    
                    window.ezpbCache=true;
                    
                    
                    var __banger_pmp_deals=function(){var d={};return[{"SlotName":"/***********,22569517314/plantuml_com-large-billboard-2","Deals":[]},{"SlotName":"/***********,22569517314/plantuml_com-medrectangle-2","Deals":[]},{"SlotName":"/***********,22569517314/plantuml_com-square-1","Deals":[]}]}();
                    
                     
                    
                    
                    
                    (function() {
                      var script = document.createElement('script');
                      script.type = 'text/javascript';
                      script.src = 'https://link.rubiconproject.com/magnite/21150.js';
                      document.head.appendChild(script);
                    })();
                    
                    
                    var __ez_gcb="gcb=195-2&cb=648";"use strict";window.googletag=window.googletag||{};googletag.cmd=googletag.cmd||[];__ez.fads=window.__ez.fads||{cmd:[],initslots:{},kvStore:{},divs:[[],[],[],[],[],[],[]],divsd:[],fadcount:0,isJIT:true,};var ez_ad_units=ez_ad_units||[];var ezslots=[];var ezrpos=[];var ezsrqt={};var __ez_fad_haspo=false;var __ez_fad_hascp=false;if(typeof PerformanceObserver!=='undefined'&&typeof PerformanceObserver.supportedEntryTypes!=='undefined'){if(PerformanceObserver.supportedEntryTypes.indexOf('largest-contentful-paint')>-1){__ez_fad_haspo=true;}}
                    try{var __ez_fad_po=new PerformanceObserver(function(){window.__ez_fad_hascp=true;__ez.fads.cmd.push(function(){__ez.fads.__ez_fad_hascp=true;});});__ez_fad_po.observe({type:'largest-contentful-paint',buffered:true});}catch(e){console.log(e);}
                    function __ez_fad_position(id){__ez.fads.cmd.push(function(){__ez.fads.__ez_fad_position(id);});}
                    function ezSetTargetingFromMap(slot,obj){if(typeof slot==='undefined'){return;}
                    for(var key in obj){if(!obj.hasOwnProperty(key)){continue;}
                    slot.setTargeting(key,obj[key]);}}
                    function ezSetSlotTargeting(divid,key,value){var slot=ezGetSlotById(divid);if(slot){if(typeof __ez.fads.kvStore[divid]!=='undefined'&&typeof __ez.fads.kvStore[divid][key]!=='undefined'){delete __ez.fads.kvStore[divid][key];}
                    slot.setTargeting(key,value);}else{if(typeof __ez.fads.kvStore[divid]==='undefined'){__ez.fads.kvStore[divid]={};}
                    __ez.fads.kvStore[divid][key]=value;var translatedId=divid;if(typeof __ez.fads!='undefined'&&__ez.fads.isJIT==true){translatedId=__ez.fads.GetGAMTranslatedId(divid);}
                    if(translatedId!=divid){if(typeof __ez.fads.kvStore[translatedId]==='undefined'){__ez.fads.kvStore[translatedId]={};}
                    __ez.fads.kvStore[translatedId][key]=value;}}}
                    function ezGetSlotById(id){if(typeof window.ezslots==='undefined'||window.ezslots==0){return;}
                    var translatedId=id;if(typeof __ez.fads!='undefined'&&__ez.fads.isJIT==true){translatedId=__ez.fads.GetGAMTranslatedId(id);}
                    for(var i=0;i<window.ezslots.length;i++){var slot=window[ezslots[i]];if(typeof slot==='undefined'){continue;}
                    var slotId=slot.getSlotElementId();if(typeof slotId!=='undefined'&&(slotId==id||slotId==translatedId)){return slot;}}}
                    function __ez_close_anchor(){googletag.cmd.push(function(slot){for(var i=0;i<window.ezslots.length;i++){var slot=window[ezslots[i]];if(typeof slot==='undefined'){continue;}
                    var alS=slot.getTargeting('al')[0]%1000;if(alS==5){googletag.destroySlots([slot]);}}
                    if(typeof window.ezdomain!=='undefined'){var d=new Date();d.setTime(d.getTime()+(2*60*60*1000));var expires="expires="+d.toGMTString();var cookie="ez_anchor_closed=true;domain=."+ezdomain+';'+expires+"; path=/";document.cookie=cookie;}
                    if(typeof(__ez_set_outstream_floor)!=='undefined'){__ez_set_outstream_floor(0);}
                    var anchor=document.getElementById('ezmobfooter');if(!anchor){return;}
                    anchor.innerHTML='';anchor.style.paddingTop=0;var styleElement=document.getElementById('ezoicCSS');if(!styleElement){return;}
                    var cert=document.getElementById('ezoic-certification-fixed');if(cert){cert.style.bottom='15px';}
                    var styles=styleElement.sheet?styleElement.sheet:styleElement.styleSheet;for(var i=0;i<styles.cssRules.length;i++){var rules=styles.cssRules[i];if(rules.selectorText==='body'&&rules.style.height==='auto'&&(rules.style.paddingTop!==''||rules.style.paddingBottom!=='')){styles.deleteRule(i);}}});}__ez.fads=(__ez.fads&&__ez.fads.loaded===true)?__ez.fads:{cmd:typeof window.__ez.fads!="undefined"?window.__ez.fads.cmd||[]:[],logEnabled:typeof URLSearchParams==='function'&&new URLSearchParams(window.location.search).get('ez_debug')==='jit',doc_ht:0,vp_ht:0,loaded:true,isJIT:true,libraryRoot:'https://go.ezodn.com/porpoiseant/',scrollMonitor:{loaded:false,url:'ezjitscroll.js'},positionMonitor:{loaded:false,url:'ezjitpos.js'},adLoadGAM:{loaded:false,url:'ezadloadgam.js'},adLoadAMZN:{loaded:false,url:'ezadloadamzn.js'},adLoadAS:{loaded:false,url:'ezadloadas.js'},adLoadHB:{loaded:false,url:'ezadloadhb.js'},adFilled:{loaded:false,url:'ezadfilled.js'},incontentSticky:{loaded:false,url:'ezicsticky.js'},identity:{loaded:false,url:'ezidentity.js'},amznH2Bid:{loaded:false,url:'ezamznh2bid.js'},adLoadBackfill:{loaded:false,url:'ezadloadbackfill.js'},adLoadRewarded:{loaded:false,url:'ezadloadrewarded.js'},adCreator:{loaded:false,url:'ezadcreator.js'},adTopper:{loaded:false,url:'ezadtopper.js'},directAd:{loaded:false,url:'ezdirectad.js'},bangerName:'IL11ILILIIlLLLILILLLLIILLLIIL11111LLILiiLIliLlILlLiiLLIiILL',libraries:['positionMonitor','scrollMonitor','adLoadGAM','adLoadAMZN','adLoadAS','adLoadHB','incontentSticky','adFilled','identity','amznH2Bid','adLoadBackfill','adLoadRewarded','adCreator','adTopper','directAd'],librariesLoading:[],libraryCmds:{},kvStore:typeof window.__ez.fads!="undefined"?window.__ez.fads.kvStore||{}:{},addedDivs:[],floatingAdsShown:false,loadTime:new Date().getTime(),onScreenDelay:0,isInit:false,triggerFloatingOnAdd:false,eligibleRefreshAds:[],onScreenObserver:undefined,observedElements:[],onScreenDivs:[],onScreenLoadStatus:0,callGAMOnAuctionComplete:[],runOnScreenFallback:true,headerBiddingTimeout:3000,pageDataQueued:false,pageDataCallCount:0,skipBangCheck:[],divTranslations:{},adhesionDivId:null,adsAllowedToLoad:[],loadingAdsAllowed:true,allowAllBangs:false,multiAuctionsRun:[],multiAuctionSimultaneous:3,multiAuctionGroups:{},multiAuctionFilledResponseCounts:{},multiAuctionFloors:{},multiAuctionStartingFloors:{},highValueDuplicated:[],initbids:{},responseCounter:{},replaceOnRefresh:{},interstitialFilled:false,ezoicIsBackfillOnly:window.ezoicIsBackfillOnly||window.location.hostname=="www.lpga.com"||false,initslots:typeof window.__ez.fads!="undefined"?window.__ez.fads.initslots||{}:{},divsd:typeof window.__ez.fads!="undefined"?window.__ez.fads.divsd||[]:[],divsdExt:[],version:1,__ez_fad_haspo:window.__ez_fad_haspo||false,__ez_fad_hascp:window.__ez_fad_hascp||false,init:function(){if(this.isInit){return;}
                    this.isInit=true;if(typeof window.__ez.jitver!='undefined'){this.version=window.__ez.jitver;}
                    this.initCommon();if(this.ezoicIsBackfillOnly){this.loadingAdsAllowed=false;this.loadLibrary(this.adLoadBackfill.url,'adLoadBackfill');}
                    if(window.ezdupads!==true){this.loadLibrary(this.adCreator.url,'adCreator');}
                    this.cmd=(function(commandsToRun){var newCmd={push:function(f){f();}};for(var i=0;i<commandsToRun.length;i++){if(typeof commandsToRun[i]==='function'){commandsToRun[i]();}}
                    return newCmd;})(this.cmd);if(this.version==6){this.onScreenDelay=2500;}
                    this.initOnScreenFallbacks();},reset:function(){this.loadTime=new Date().getTime();this.divsd=[];this.addedDivs=[];this.onScreenDivs=[];this.teardownOnScreenObserver();this.onScreenLoadStatus=0;this.eligibleRefreshAds=[];this.callGAMOnAuctionComplete=[];this.divsdExt=[];this.runOnScreenFallback=true;this.callLibrary('positionMonitor','reset');this.callLibrary('scrollMonitor','reset');this.callLibrary('adLoadGAM','reset');this.callLibrary('adLoadAMZN','reset');this.callLibrary('adLoadAS','reset');this.callLibrary('adLoadHB','reset');this.callLibrary('incontentSticky','reset');this.initCommon();},cleanupAdData:function(divId){this.addedDivs=this.addedDivs.filter(function(id){return id!==divId;});this.divsd=this.divsd.filter(function(id){return id!==divId;});this.divsdExt=this.divsdExt.filter(function(id){return id!==divId;});this.onScreenDivs=this.onScreenDivs.filter(function(id){return id!==divId;});this.callGAMOnAuctionComplete=this.callGAMOnAuctionComplete.filter(function(id){return id!==divId;});this.eligibleRefreshAds=this.eligibleRefreshAds.filter(function(id){return id!==divId;});this.onScreenLoadStatus=0;this.runOnScreenFallback=true;delete this.initslots[divId];delete this.kvStore[divId];this.callLibrary('positionMonitor','cleanupAdData',[divId]);this.callLibrary('adLoadGAM','cleanupAdData',[divId]);this.callLibrary('adLoadHB','cleanupAdData',[divId]);},RemoveAd:function(divId){this.adLoadGAM.destroySlot(divId);this.cleanupAdData(divId);this.RemoveIdFromMultiAuction(divId);var elem=document.getElementById(divId);if(elem){elem.remove();}},initCommon:function(){this.doc_ht=this.__ez_fad_docht();this.vp_ht=this.__ez_fad_vpht();if(this.__ez_fad_scroll()>0){this.handleScroll();}
                    this.initOnScreenObserver();this.initializePositions();this.handleEventListeners();},loadLibrary:function(url,name){if(this.librariesLoading.indexOf(name)==-1&&typeof this[name]!="undefined"&&this[name].loaded!==true){if(document==null||typeof document.body=='undefined'||document.body==null){setTimeout(function(){__ez.fads.loadLibrary(url,name);},100);return;}
                    this.librariesLoading.push(name);var script=document.createElement('script');script.src=this.libraryRoot+url+"?"+this.getCacheBustParams();script.async=true;script.onload=function(){__ez.fads.LibraryLoaded(name);};document.body.appendChild(script);}},LibraryLoaded:function(name){if(this.libraries.indexOf(name)==-1){return;}
                    this[name].loaded=true;if(typeof this[name].init==='function'){this[name].init(this.vp_ht,this.doc_ht,this.version);}
                    this.runLibraryCmds(name);},runLibraryCmds:function(name){if(typeof this.libraryCmds[name]!=='undefined'){for(var i=0;i<this.libraryCmds[name].length;i++){this.libraryCmds[name][i]();}
                    this.libraryCmds[name]=[];}},callLibrary:function(name,func,args,skipLibraryLoad){if(typeof args!='undefined'&&typeof args.length=='undefined'){args=[args];}
                    if(this.libraries.indexOf(name)==-1){return;}
                    if(typeof this[name]!='undefined'&&this[name].loaded==true&&typeof this[name][func]=='function'){return this[name][func].apply(this[name],args);}else{this.libraryCmds[name]=this.libraryCmds[name]||[];this.libraryCmds[name].push(function(){__ez.fads[name][func].apply(__ez.fads[name],args);});if(skipLibraryLoad!==true){this.loadLibrary(this[name].url,name);}}},__ez_fad_docht:function(){return document.documentElement.scrollHeight},__ez_fad_vpht:function(){if(typeof window.innerHeight!='undefined'){return window.innerHeight;}
                    if(typeof document.documentElement!='undefined'){return document.documentElement.clientHeight;}
                    var body=typeof document.body!='undefined'&&document.body!=null?document.body:document.getElementsByTagName('body')[0];var screenHeight=typeof window.screen!='undefined'?window.screen.height:0;return Math.min(body.clientHeight,screenHeight)||0;},__ez_fad_scroll:function(){return window.pageYOffset||(document.documentElement||document.body.parentNode||document.body).scrollTop},getCacheBustParams:function(){if(typeof window.__ez_gcb=='undefined'){var d=new Date();gcb='gcb='+d.getFullYear()+d.getMonth()+d.getDate()+d.getHours();cb='0';return gcb+"&"+cb;}else{return window.__ez_gcb}},initializePositions:function(){if(typeof window.ez_ad_units=='undefined'){setTimeout(function(){__ez.fads.initializePositions();},100);return;}
                    for(var i=0;i<window.ez_ad_units.length;i++){var unit=window.ez_ad_units[i];this.__ez_fad_position('div-gpt-ad-'+unit[6]);}},SetViewportHeight:function(viewportHeight){this.vp_ht=viewportHeight;this.callLibrary('positionMonitor','setViewportHeight',[viewportHeight]);},SetDocumentHeight:function(documentHeight){this.doc_ht=documentHeight;this.callLibrary('positionMonitor','setDocumentHeight',[documentHeight]);},handleScroll:function(){this.callLibrary('scrollMonitor','SetAdLoadFunctions',[this.__ez_fad_load.bind(this)]);this.loadFloatingAds(this.headerBiddingTimeout+500);setTimeout(function(){window.__ez.fads.loadInterstitial();},2000);if(this.onScreenLoadStatus==0){this.loadOnScreenAds();this.onScreenLoadStatus=1;}
                    this.callLibrary('incontentSticky','CheckIncontentSticky',[]);},loadInterstitial:function(){if(!window.ezslot_interstitial&&window.ezslot_init_interstitial){window.ezslot_init_interstitial();}},__ez_fad_position:function(id){id=this.getCleanDivId(id);if(this.runOnScreenFallback){this.runOnScreenFallback=false;this.initOnScreenFallbacks();}
                    if(document.getElementById(id)==null&&this.ezoicIsBackfillOnly!==true){return;}
                    if(this.addedDivs.indexOf(id)!=-1){return;}
                    this.callLibrary('positionMonitor','AddDiv',[id,4],true);this.__ez_fad_add(id);if(!this.isFloating(id)&&!this.isPixel(id)&&this.isOnScreen(id)){if(this.onScreenLoadStatus!==0){this.loadAdsWaitForExternal([id]);}}else if(this.onScreenLoadStatus==0&&this.version!=6){setTimeout(function(){this.loadOnScreenAds();}.bind(this),200);this.onScreenLoadStatus=1;}else{setTimeout(function(){this.callLibrary('positionMonitor','detectPosition',[id]);}.bind(this),200);}},initOnScreenFallbacks:function(){setTimeout(function(){if(__ez.fads.onScreenLoadStatus==false){__ez.fads.loadOnScreenAds(true);}},1000+this.onScreenDelay);setTimeout(function(){window.__ez.fads.loadInterstitial();},+this.onScreenDelay+2500);if(this.__ez_fad_rdy()==false){document.addEventListener('DOMContentLoaded',function(){setTimeout(function(){if(__ez.fads.onScreenLoadStatus==false){__ez.fads.loadOnScreenAds(true);}},1000+__ez.fads.onScreenDelay);}.bind(this));}},handleEventListeners:function(){__ez.fads.interactionEvents=__ez.fads.GetInteractionEvents();function __ez_handle_init_scroll(e){__ez.fads.handleScroll();for(var i=0;i<__ez.fads.interactionEvents.length;i++){window.removeEventListener(__ez.fads.interactionEvents[i],__ez_handle_init_scroll);}}
                    for(var ieIdx=0;ieIdx<__ez.fads.interactionEvents.length;ieIdx++){window.addEventListener(__ez.fads.interactionEvents[ieIdx],__ez_handle_init_scroll);}},LoadFallbackAd:function(id,slot_name){if(window.__ez.fads.adLoadBackfill.loaded==true&&typeof window.__ez.fads.adLoadBackfill.LoadAd=='function'){if(__ez.fads.adLoadBackfill.standaloneLocationsCalled.indexOf(id)>-1){__ez.fads.adLoadBackfill.standaloneLocationsCalled.splice(__ez.fads.adLoadBackfill.standaloneLocationsCalled.indexOf(id),1);}
                    __ez.fads.adLoadBackfill.LoadAd(id,slot_name);}else{this.loadingAdsAllowed=false;this.loadLibrary(this.adLoadBackfill.url,'adLoadBackfill');this.ezoicIsBackfillOnly=true;setTimeout(function(){__ez.fads.LoadFallbackAd(id,slot_name);},100);}},loadDirectAd:function(){if(window.ezslot_init_interstitial&&window._ezaq&&window._ezaq['country']=='US'&&Math.random()<0.3){if(this.interstitialFilled!=true){this.loadLibrary(this.directAd.url,'directAd');}}},loadHighValueAds:function(){var highValueAds=this.getHighValueAds();highValueAds=this.filterAdsAllowedToLoad(highValueAds);highValueAds=highValueAds.filter(function(id){return this.isGAMOnAuctionComplete(id)==false;}.bind(this));if(highValueAds.length>0){this.loadAdsWaitForExternal(highValueAds);}},loadOnScreenAds:function(force){if(this.onScreenLoadStatus==2&&!force){return;}
                    var floatAds=window.__ez_fad_floating||[];var divs=this.onScreenDivs.concat(floatAds);var highValueAds=this.getHighValueAds();divs=divs.concat(highValueAds);divs=divs.filter(function(value,index,self){return self.indexOf(value)===index;});divs=this.filterAdsAllowedToLoad(divs);if(divs.length>0){this.markIdsAsRunGAMOnAuctionComplete(divs);this.onScreenLoadStatus=2;setTimeout(function(){this.loadAdsWaitForExternal(divs);}.bind(this),this.onScreenDelay);}
                    setTimeout(function(){window.__ez.fads.loadInterstitial();},2200);setTimeout(function(){window.__ez.fads.loadHighValueAds();},3200);setTimeout(function(){window.__ez.fads.loadDirectAd();},4200);},filterAdsAllowedToLoad:function(ids){if(this.ezoicIsBackfillOnly==true){return ids.filter(function(id){return this.adsAllowedToLoad.indexOf(id)!=-1;}.bind(this));}else{return ids.filter(id=>{const element=document.getElementById(id);return!(element&&element.querySelector('.adsbygoogle.ezasloaded'));});}},isAdAllowedToLoad:function(id){if(this.ezoicIsBackfillOnly==true||this.isFloating(id)){return this.adsAllowedToLoad.indexOf(id)!=-1;}else{return true;}},AddAllowedAd:function(id){this.adsAllowedToLoad.push(id);this.loadingAdsAllowed=true;},loadAdsWaitForExternal:function(ids,isInitailLoad){if(this.adLoadHB.loaded==true){var auctionIds=this.adLoadHB.FilterByAuctionEligible(ids);}else{var auctionIds={eligible:ids,notEligible:[],running:[]};}
                    if(auctionIds.eligible.length>0){this.markIdsAsRunGAMOnAuctionComplete(auctionIds.eligible);this.callExternalBidders(auctionIds.eligible,true);}
                    if(auctionIds.notEligible.length>0){if(isInitailLoad){this.callLibrary('adLoadGAM','LoadAd',[auctionIds.notEligible]);}else{this.refreshAds(auctionIds.notEligible);}}
                    if(auctionIds.running.length>0){var loadIds=[];for(var i=0;i<auctionIds.running.length;i++){if(this.isGAMOnAuctionComplete(auctionIds.running[i])==false){loadIds.push(auctionIds.running[i]);}}
                    if(loadIds.length>0){setTimeout(function(){if(isInitailLoad){this.callLibrary('adLoadGAM','LoadAd',[loadIds]);}else{this.refreshAds(loadIds);}}.bind(this),1000);}}},initOnScreenObserver:function(){if(this.onScreenObserver)return;this.onScreenObserver=new IntersectionObserver(entries=>{entries.forEach(entry=>this.handleIntersection(entry));},{root:null,threshold:0});},handleIntersection:function(entry){const id=entry.target.id;const index=this.onScreenDivs.indexOf(id);if(entry.isIntersecting){if(index===-1){this.onScreenDivs.push(id);}}else{if(index!==-1){this.onScreenDivs.splice(index,1);}}},observeElement:function(id){const elem=document.getElementById(id);if(!elem)return;if(!this.observedElements.includes(id)){this.onScreenObserver.observe(elem);this.observedElements.push(id);if(this.isElementInViewport(elem)){this.onScreenDivs.push(id);}}},isOnScreen:function(id){if(!this.observedElements.includes(id)){this.observeElement(id);}
                    const isOnScreen=this.onScreenDivs.includes(id);return isOnScreen;},isBangSkip:function(id){return this.skipBangCheck.indexOf(id)!==-1;},isElementInViewport:function(element){const rect=element.getBoundingClientRect();return(rect.top>=0&&rect.left>=0&&rect.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&rect.right<=(window.innerWidth||document.documentElement.clientWidth));},teardownOnScreenObserver:function(){if(this.onScreenObserver){this.onScreenObserver.disconnect();this.onScreenObserver=undefined;this.observedElements=[];}},LoadAds:function(ids,force,waitForExternal){for(var i=0;i<ids.length;i++){this.LoadAd(ids[i],force,waitForExternal);}},AllowAdLoad:function(){this.loadingAdsAllowed=true;},EnsureSlotInitialized:function(id){if(typeof window.ezslots=='undefined'){return false;}
                    var slotElementIds=[];ezslots.forEach(function(slotName){let slot=window[slotName];if(slot&&typeof slot.getSlotElementId==='function'){let slotElementId=slot.getSlotElementId();slotElementIds.push(slotElementId);}});if(typeof slotElementIds=='undefined'||slotElementIds.includes(id)==false){let initslotFunction=__ez.fads.initslots[id];if(typeof initslotFunction==='function'){initslotFunction();if(typeof this.initbids[id]==='function'){this.initbids[id]();}}}
                    return true;},LoadAd:function(id,force,waitForExternal){if(!force&&!this.__ez_fad_rdy()){setTimeout(function(){__ez.fads.LoadAd(id,force,waitForExternal);},50);return;}
                    if(this.isAdAllowedToLoad(id)==false){return;}
                    if(__ez.fads.divsd.indexOf(id)==-1){this.EnsureSlotInitialized(id);__ez.fads.divsd.push(id);if(this.handleAdsense(id)){this.removeLoadingIcon(id);return;}
                    this.loadLibrary(this.adLoadGAM.url,'adLoadGAM');if(waitForExternal==true){this.loadAdsWaitForExternal([id],true);}else{if(!this.isRewardedAd(id)){this.callExternalBidders(id);}
                    this.callLibrary('adLoadGAM','LoadAd',[id]);}}else if(this.isEligibleAdRefresh(id)){this.refreshAds([id]);}else{}},refreshAds:function(ids){this.callLibrary('adLoadGAM','RefreshAds',[ids]);for(var i=0;i<ids.length;i++){this.removeEligibleAdRefresh(ids[i]);}},removeLoadingIcon:function(id){var adDiv=document.getElementById(id);if(adDiv){adDiv.classList.remove("ezoic-adl");}},isDynamicAd:function(id){return id.indexOf('-dynamic')!==-1;},isMultiAd:function(id){return id.indexOf('-multi')!==-1&&id.indexOf('-highvalue')===-1;},isFloating:function(id){if(typeof __ez_fad_floating!=='undefined'){if(Array.isArray(__ez_fad_floating)){return __ez_fad_floating.includes(id);}
                    for(let key in __ez_fad_floating){if(Array.isArray(__ez_fad_floating[key])&&__ez_fad_floating[key].includes(id)){return true;}}}
                    if(typeof __ez_fad_floating==='undefined'&&(id.indexOf('div-gpt-ad-Adhesion')!==-1||id.indexOf('-medrectangle-2-0')!==-1)){return true;}
                    return false;},isPixel:function(id){return id.indexOf('-pixel')!==-1;},isRewardedAd:function(id){return id.indexOf('square-1')!==-1||id.indexOf('Sidebar_3')!==-1;},callExternalBidders:function(ids,force){if(!Array.isArray(ids)){ids=[ids];}
                    if(force!==true){var ids=ids.filter(function(id){return __ez.fads.divsdExt.indexOf(id)==-1||__ez.fads.isEligibleAdRefresh(id)==true;});}
                    if(ids.length==0){return;}
                    this.divsdExt=this.divsdExt.concat(ids);var br1s=this.getRawSlotsById(ids,'br1');this.callLibrary('adLoadHB','LoadAd',[ids,0,br1s,this.headerBiddingTimeout]);if(typeof window.__ezaps!='undefined'&&window.__ezaps.length>0||typeof __ez_hasamzn!='undefined'&&__ez_hasamzn==true){this.callLibrary('adLoadAMZN','LoadAd',[ids,0,br1s]);}
                    if(typeof openwrapRequestAdUnits==='function'){if(typeof window.__ezPWTAdUnits!='undefined'){var slotsToRequest=ids.map(id=>window.__ezPWTAdUnits[id]);slotsToRequest=slotsToRequest.filter(slot=>slot!=="");openwrapRequestAdUnits(slotsToRequest);}}},SetGAMTargeting:function(id,key,value){var gamId=this.getDivTranslation(id);this.callLibrary('adLoadGAM','SetTargeting',[gamId,key,value]);},getCleanSlotId:function(id){var slotId=id;if(id.includes("div-gpt-ad-")){var slotId=id.split('div-gpt-ad-')[1].split('-',3).join('-');if(id.includes("/")){slotId=id.split('div-gpt-ad-')[1];slotId=slotId.slice(0,slotId.lastIndexOf('-'));}}else if(id.includes("/")){slotId=id.split('/').pop();slotId=slotId.slice(0,slotId.lastIndexOf('_'));}
                    return slotId;},GetGAMTranslatedId:function(id){return this.getDivTranslation(id);},GetEzoicDivId:function(id){return this.getCleanDivId(id);},getCleanDivId:function(id){if(typeof id!=='string'){return id;}
                    var transId=this.getInternalDivTranslation(id);if(transId!==id){return transId;}
                    let slotId=id;if(typeof window.gamAnchorPosition!=='undefined'&&id.includes("div-gpt-ad-Adhesion/")&&(window.gamAnchorPosition==="bottom"||window.gamAnchorPosition==="top")){if(this.adhesionDivId==null){this.adhesionDivId=id;}}else if(id.includes("gpt_unit_")&&id.includes("Adhesion")){if(this.adhesionDivId!=null){this.storeDivTranslation(this.adhesionDivId,slotId);slotId=this.adhesionDivId;}}
                    else if(id.includes("gpt_unit_")&&id.includes("/")){let cleaned=id.replace("gpt_unit_","");cleaned=cleaned.replace(/^\/+/,"");const firstSlashPos=cleaned.indexOf("/");if(firstSlashPos!==-1){cleaned=cleaned.substring(firstSlashPos+1);}
                    const underscorePos=cleaned.lastIndexOf("_");if(underscorePos!==-1){cleaned=cleaned.substring(0,underscorePos)
                    +"-"
                    +cleaned.substring(underscorePos+1);}
                    slotId="div-gpt-ad-"+cleaned;}
                    if(id!==slotId){this.storeDivTranslation(slotId,id);}
                    return slotId;},getRawSlotValue:function(id,key){var rawSlot=this.getRawSlotsById([id],key);return rawSlot[id];},getRawSlotsById:function(ids,key){var rawSlots={};if(typeof window.ezslots_raw!='undefined'){for(var i=0;i<ids.length;i++){var id=ids[i];var slotId=this.getCleanSlotId(id);slotId=slotId.replace(/^(.*-\d+)-\d+$/,'$1');for(var s in window.ezslots_raw){if(typeof window.ezslots_raw[s].tap!="undefined"&&window.ezslots_raw[s].tap.includes(slotId)){rawSlots[id]=window.ezslots_raw[s];}}}
                    if(key!=null){for(var k in rawSlots){rawSlots[k]=rawSlots[k][key];}}}
                    return rawSlots;},storeDivTranslation:function(id,translation){this.divTranslations[id]=translation;},getDivTranslation:function(id){if(typeof this.divTranslations[id]=='undefined'){return id;}
                    return this.divTranslations[id];},getInternalDivTranslation:function(id){for(var key in this.divTranslations){if(this.divTranslations[key]==id){return key;}}
                    return id;},AdLoadComplete:function(library,ids){if(library=='adLoadHB'){var loadIds=[];var refreshIds=[];for(var i=0;i<ids.length;i++){if(this.isGAMOnAuctionComplete(ids[i])){if(!this.isFloating(ids[i])&&!this.isDynamicAd(ids[i])){var floor=this.getRawSlotValue(ids[i],'br1');if(typeof floor=='string'){floor=parseFloat(floor);}
                    if(typeof floor=='number'&&floor>=50){var newIds=this.RunMultiAuction(ids[i]);if(newIds&&newIds.length>0){for(var j=0;j<newIds.length;j++){this.EnsureSlotInitialized(newIds[j]);this.divsd.push(newIds[j]);}}}else{this.log("not running multi auction for",ids[i],floor);}}
                    if(this.eligibleRefreshAds.indexOf(ids[i])!=-1){refreshIds.push(ids[i]);}else{loadIds.push(ids[i]);}
                    this.removeGAMOnAuctionComplete(ids[i]);}else{}}
                    if(loadIds.length>0){this.callLibrary('adLoadGAM','LoadAd',[loadIds]);}
                    if(refreshIds.length>0){this.refreshAds(refreshIds);}}},AdFilled:function(id,event){id=this.getCleanDivId(id);this.recordMultiAuctionFill(id);this.log("ad filled",id);__ez.fads.logAdStats(id,'filled');this.callLibrary('adFilled','AdFilled',[id,event]);this.QueuePageData();},adFill:function(id){if(this.adFilled.loaded==true){if(window.ezincfill){this.callLibrary('adLoadGAM','IncreaseFloor',[id,3]);}else{this.callLibrary('adLoadGAM','IncreaseFloor',[id,2]);}
                    var baseId=this.getBaseId(id);var duplicatedId=null;var IsTopped=false;var isHighValue=false;if(this.adTopper.loaded){IsTopped=this.adTopper.IsTopped(id);}
                    if(this.adFilled.IsHighValueFilled(id)){isHighValue=true;}
                    if(isHighValue){this.updateFloatingAdExpansion();}
                    if(this.isFloating(id)==false&&this.highValueDuplicated.indexOf(baseId)==-1){if((isHighValue)&&!IsTopped){var highestFilledRate=this.adFilled.GetHighestFilledRate(id);duplicatedId=this.duplicateAdPlacement(id,false,1.0,highestFilledRate,0,true);this.log("** high value ad filled - duplicating",id,highestFilledRate,duplicatedId);if(duplicatedId!=false){this.highValueDuplicated.push(baseId);}
                    this.increaseLazyLoad();}
                    if(window.ezadtoppers&&!duplicatedId&&this.getPositionId(id)!=1){this.callLibrary('adTopper','CreateSlideshow',[id]);if(isHighValue){setTimeout(function(){__ez.fads.callLibrary('adTopper','EnableAutoFlip',[id]);},5000);}}}
                    if(window.ezocolaver==2&&this.isFloating(id)){this.SetGAMTargeting(id,'cal','2');}else if(window.ezocolaver==3&&this.isFloating(id)&&this.adFilled.IsAboveFilled(id,.75)){this.SetGAMTargeting(id,'cal','2');}}},updateFloatingAdExpansion:function(){if(window.ezocolaver==4){var floatingAds=window.__ez_fad_floating||[];for(var i=0;i<floatingAds.length;i++){this.SetGAMTargeting(floatingAds[i],'cal','2');}}},increaseLazyLoad:function(){this.allowAllBangs=true;if(window.ezinclazy==true){googletag.pubads().enableLazyLoad({fetchMarginPercent:1000,renderMarginPercent:1000,mobileScaling:2,});}},QueuePageData:function(){if(this.pageDataQueued==false){this.pageDataQueued=true;var time=10000;if(this.pageDataCallCount>0){time=10000;}
                    setTimeout(function(){__ez.fads.RecordPageData();},time);}},RecordPageData:function(){if(this.adFilled.loaded==false){setTimeout(function(){__ez.fads.callLibrary('identity','RecordPageData');return;});}else{this.pageDataQueued=false;this.callLibrary('identity','RecordPageData');}},GetPageTotals:function(){return this.adFilled.GetPageTotals();},__ez_fad_load:function(adLoadBounds,force){if(!force&&!this.__ez_fad_rdy()){return;}
                    if(this.positionMonitor.loaded==false){this.loadLibrary(this.positionMonitor.url,'positionMonitor');setTimeout(function(){__ez.fads.__ez_fad_load(adLoadBounds,force);},50);return;}
                    var divs=this.positionMonitor.getPositionsSorted();var s=[];var loadIds=[];var externalIds=[];for(var i=0;i<divs.length;i++){if(this.divsd.indexOf(divs[i])!=-1&&this.isEligibleAdRefresh(divs[i])==false){continue;}
                    if(this.positionMonitor.isInBounds(divs[i][0],adLoadBounds)){loadIds.push(divs[i][0]);}else if(this.positionMonitor.isInBounds(divs[i][0],adLoadBounds,true)){externalIds.push(divs[i][0]);}}
                    if(this.positionMonitor.isUniversalVideoPlayerInBounds(adLoadBounds)){}
                    if(loadIds.length>0){for(var i=0;i<loadIds.length;i++){this.LoadAd(loadIds[i],force,true);}}
                    if(externalIds.length>0){this.callExternalBidders(externalIds);}},handleAdsense:function(id){var ezslotName=window.ezslotdivs&&ezslotdivs[id]&&ezslotdivs[id].slot;if(typeof ezslotName!='undefined'){var rawTargeting=window[ezslotName+'_raw'];if(rawTargeting&&rawTargeting['compid']=='1'){if(typeof window.ezasBuild==='function'&&window.ezasBuild(id)){__ez.fads.divsd.push(id);return true;}
                    rawTargeting['compid']='0';}}
                    return false},addEligibleAdRefresh:function(id){if(this.eligibleRefreshAds.indexOf(id)==-1){this.eligibleRefreshAds.push(id);}},removeEligibleAdRefresh:function(id){if(this.eligibleRefreshAds.indexOf(id)!=-1){this.eligibleRefreshAds.splice(this.eligibleRefreshAds.indexOf(id),1);}},isEligibleAdRefresh:function(id){return this.eligibleRefreshAds.indexOf(id)!=-1;},addGAMOnAuctionComplete:function(id){if(this.callGAMOnAuctionComplete.indexOf(id)===-1){this.callGAMOnAuctionComplete.push(id);}},removeGAMOnAuctionComplete:function(id){var index=this.callGAMOnAuctionComplete.indexOf(id);if(index!==-1){this.callGAMOnAuctionComplete.splice(index,1);}},isGAMOnAuctionComplete:function(id){return this.callGAMOnAuctionComplete.indexOf(id)!==-1;},markIdsAsRunGAMOnAuctionComplete:function(ids){for(var i=0;i<ids.length;i++){if(!this.isGAMOnAuctionComplete(ids[i])){this.addGAMOnAuctionComplete(ids[i]);}
                    if(this.divsd.indexOf(ids[i])==-1){this.divsd.push(ids[i]);}}},RefreshAdFromPlaceholderId:function(placeholderId,skipExternal,force){},RefreshAd:function(id,skipExternal,force){id=this.getCleanDivId(id);if(!isNaN(id)){var ids=this.getIdsFromPlaceholderId(id);for(var i=0;i<ids.length;i++){this.RefreshAd(ids[i],skipExternal,force);}
                    return;}
                    var replacementSize=this.getReplaceOnRefreshSize(id);if(replacementSize!=null){this.log("replacing ad instead of refreshing. (replace on refresh)",id,replacementSize);this.removeReplaceOnRefresh(id);this.adCreator.ReplaceAd(id,replacementSize);return;}
                    gamId=this.getDivTranslation(id);this.callLibrary('adLoadGAM','resetTargeting',[gamId]);this.addEligibleAdRefresh(id);if(force===true){this.skipBangCheck.push(id);}
                    if(skipExternal!==true&&force!==true){this.addGAMOnAuctionComplete(id);this.callExternalBidders(id);}else{this.LoadAd(id,true,false);}},getIdsFromPlaceholderId:function(placeholderId){if(typeof ez_ad_units==='undefined'){return;}
                    var slotArray=[];var pcAdUnits=ez_ad_units.filter((adUnit)=>{return adUnit.length>=4?placeholderId===adUnit[3]:false;});for(var i=0;i<pcAdUnits.length;i++){slotArray.push('div-gpt-ad-'+pcAdUnits[i][6]);}
                    return slotArray;},ShouldResize:function(id){if(this.isOnScreen(id)){return false;}
                    return true;},isHighValueAd:function(id){if(this.isRewardedAd(id)){return false;}
                    var floor=0;var bid=0;if(this.adLoadGAM.loaded==true){floor=this.adLoadGAM.GetFloorFromId(id);bid=this.adLoadGAM.GetBidFromId(id);if(floor>=10||bid>=5){return true;}else{}}else{}
                    return false;},getHighValueAds:function(){var ids=[];var isHigh=false;for(var i=0;i<this.divsd.length;i++){isHigh=this.isHighValueAd(this.divsd[i]);if(isHigh){ids.push(this.divsd[i]);}}
                    return ids;},ShouldRefresh:function(id){id=this.getCleanDivId(id);if(this.isHighValueAd(id)){return true;}
                    if(!this.isFloating(id)&&!this.isPixel(id)&&!this.isOnScreen(id)){return false;}
                    return true;},getBaseId:function(id){if(typeof id!='string'){this.log("getBaseId: id is not a string",id);return id;}
                    return id.replace('-dynamic','').replace(/-multi-\d+/,'').replace('-highvalue','');},ShouldBang:function(id,slot,attempt){id=this.getCleanDivId(id);var forceAllow=false;if(this.isHighValueAd(id)||this.allowAllBangs){forceAllow=true;}
                    if(this.isMultiAuction(id)&&this.getAuctionFillCount(id)<1){return false;}
                    if(!forceAllow&&!this.isFloating(id)&&!this.isPixel(id)&&!this.isBangSkip(id)&&this.version!=97){if(!this.isOnScreen(id)&&this.positionMonitor.loaded&&this.scrollMonitor.loaded&&this.positionMonitor.isInBounds(id,this.scrollMonitor.GetAdLoadBounds(),true)==false){this.addEligibleAdRefresh(id);return false;}else{}}
                    if(this.isMultiAuction(id)){var responseCount=this.getMultiResponseCount(id);var nf=this.getNextMultiAuctionFloor(id);this.adLoadGAM.setFloorById(id,nf/100);this.adLoadGAM.SetTargeting(id,'ic',responseCount);}else{this.adLoadGAM.IncrementImpressionCount(id);}
                    return true;},Bang:function(id,attempt){window[this.bangerName].RefreshById(id,attempt);},loadFloatingAds:function(timeout,force){if(this.__ez_fad_rdy()||force){if(!this.floatingAdsShown&&typeof window.__ez_fad_floating!=='undefined'&&window.__ez_fad_floating.length>0){this.floatingAdsShown=true;setTimeout(function(){window.__ez.fads.callLibrary('adLoadGAM','ShowFloatingAds');},timeout);}else if(!this.floatingAdsShown){this.triggerFloatingOnAdd=true;}}},isMultiAuction:function(id){return this.multiAuctionsRun.indexOf(id)!=-1||(id.indexOf('-multi-')!==-1&&id.indexOf('-highvalue')==-1);},AddIdToMultiAuction:function(originalId,id){if(typeof this.multiAuctionGroups[originalId]=='undefined'){this.multiAuctionGroups[originalId]=[originalId];}
                    if(this.multiAuctionGroups[originalId].indexOf(id)==-1){this.multiAuctionGroups[originalId].push(id);}},RemoveIdFromMultiAuction:function(id){for(var key in this.multiAuctionGroups){if(this.multiAuctionGroups[key].indexOf(id)!=-1){this.multiAuctionGroups[key].splice(this.multiAuctionGroups[key].indexOf(id),1);}}
                    if(typeof this.responseCounter[id]!='undefined'){delete this.responseCounter[id];}},GetMultiAuctionIds:function(id){var baseId=this.getBaseId(id);if(typeof this.multiAuctionGroups[baseId]!='undefined'){return this.multiAuctionGroups[baseId];}
                    return[];},getNextMultiAuctionFloor:function(id){id=this.getBaseId(id);if(typeof this.multiAuctionFloors[id]=='undefined'){return 0;}
                    var nf=0;for(var i=0;i<this.multiAuctionFloors[id].length;i++){if(this.multiAuctionFloors[id][i]>0){var nf=this.multiAuctionFloors[id][i];this.multiAuctionFloors[id].splice(i,1);break;}}
                    return nf;},createMultiAuctionFloors:function(id,floor,maxRetries){if(typeof this.multiAuctionFloors[id]!='undefined'){return;}
                    this.multiAuctionStartingFloors[id]=floor;var floors=[];for(let i=1;i<=maxRetries;i++){const current_floor=floors[floors.length-1]!==undefined?floors[floors.length-1]:floor;const decreaseAmount=this.currentDecrement(i,maxRetries,floor);const newFloor=(current_floor-decreaseAmount).toFixed(2);floors.push(newFloor);}
                    this.multiAuctionFloors[id]=floors;this.log("createMultiAuctionFloors",id,floors);},cumulativeDecrement:function(i,maxRetries,floor,k=4){return floor*((1-Math.exp(-k*(i/maxRetries)))/(1-Math.exp(-k)));},currentDecrement:function(i,maxRetries,floor){if(i===1){return this.cumulativeDecrement(1,maxRetries,floor);}else{return this.cumulativeDecrement(i,maxRetries,floor)-this.cumulativeDecrement(i-1,maxRetries,floor);}},hasMultiAuctionGroupCompletedCycle:function(id){var auctionGroupIds=this.GetMultiAuctionIds(id);if(!auctionGroupIds.length){return false;}
                    var responses=[];for(var i=0;i<auctionGroupIds.length;i++){responses.push(this.responseCounter[auctionGroupIds[i]]||0);}
                    var auctionFillCount=this.getAuctionFillCount(id);for(var i=0;i<responses.length;i++){if((responses[i]!=responses[0]&&auctionFillCount==0)||responses[i]==0||responses[i]<auctionFillCount){return false;}}
                    return true;},getMultiResponseCount:function(id){auctionGroupIds=this.GetMultiAuctionIds(id);var count=0;for(var i=0;i<auctionGroupIds.length;i++){count+=this.responseCounter[auctionGroupIds[i]]||0;}
                    return count;},recordMultiAuctionFill:function(id){var responses=this.responseCounter[id]||1;var baseId=this.getBaseId(id);if(typeof this.multiAuctionFilledResponseCounts[baseId]=='undefined'||responses<this.multiAuctionFilledResponseCounts[baseId]){this.multiAuctionFilledResponseCounts[baseId]=responses;}},getAuctionFillCount:function(id){var baseId=this.getBaseId(id);return this.multiAuctionFilledResponseCounts[baseId]||0;},getMultiNumUnitsResponded:function(id){auctionGroupIds=this.GetMultiAuctionIds(id);var count=0;for(var i=0;i<auctionGroupIds.length;i++){count+=this.responseCounter[auctionGroupIds[i]]?1:0;}
                    return count;},AdServerResponseReceived:function(id,info){this.responseCounter[id]=this.responseCounter[id]?this.responseCounter[id]+1:1;var filled=!info.isEmpty;var br1LogValue=(info.slot&&info.slot.Targeting&&info.slot.Targeting['br1'])?info.slot.Targeting['br1'][0]:"N/A";this.log("AdServerResponseReceived:",id,"filled:",filled,"floor:",br1LogValue,"response num:",this.responseCounter[id],"full targeting:",info.slot.Targeting);if(this.adLoadRewarded&&this.adLoadRewarded.loaded){if(info.slot.Targeting['ap']=='9999'){this.interstitialFilled=true;this.adLoadRewarded.interstitialLoaded(info);}
                    else if(!filled&&info.slot.Targeting['pt']=='12'){this.adLoadRewarded.loadFailed(id);}}},FloatingAdded:function(){if(this.onScreenLoadStatus==2){this.loadFloatingAds(0,true);}},IsInterstitialFilled:function(){return this.interstitialFilled;},duplicateAdPlacement:function(id,force=false,floorPercentage=1.0,newFloor,delay,highValue=false){if(this.divsd.indexOf(id)==-1){return false;}
                    if(window.ezdupads!==true){return false;}
                    if(this.isDynamicAd(id)&&!this.isMultiAd(id)){return false;}
                    if(this.isDynamicAd(id)&&!highValue){return false;}
                    if(this.adCreator.loaded==false){setTimeout(function(){__ez.fads.duplicateAdPlacement(id,force,floorPercentage,newFloor,delay,highValue);},50);return;}
                    if(!this.adCreator.IsDuplicateAllowed(id,force,highValue)){return false;}
                    var auctionIdCount=1;var auctionIds=this.GetMultiAuctionIds(id);if(auctionIds.length>1){auctionIdCount=auctionIds.length;}
                    var divSuffix="-dynamic";var isMulti=false;if(highValue){divSuffix="-highvalue";}
                    if(floorPercentage!=1.0){divSuffix="-multi-"+auctionIdCount;isMulti=true;}
                    var newDivId=id+divSuffix;if(isMulti){this.AddIdToMultiAuction(id,newDivId);}
                    this.log("duplicateAdPlacement. before calling adCreator",id,floorPercentage,newDivId,newFloor,"loadAd",highValue);__ez.fads.callLibrary('adCreator','DuplicateAdPlacement',[id,force,floorPercentage,newDivId,newFloor,highValue]);return newDivId;},loadMultiAd:function(id){if(this.getAuctionFillCount(id)>0){return;}
                    this.callLibrary('adLoadGAM','LoadAd',[id]);},RunMultiAuction:function(id,num=10,attempt=1){if(window.ezmultia!=true||this.multiAuctionsRun.indexOf(id)!=-1){this.log("RunMultiAuction: not running multi auction",id,window.ezmultia,this.multiAuctionsRun.indexOf(id));return;}
                    if(this.getAuctionFillCount(id)>0){return;}
                    var maxRetries=this.getRawSlotValue(id,'bvr');if(typeof maxRetries=='undefined'&&attempt<20){this.log("**RETRYING MULTI CUZ NO RAW SLOT VALUE",id);setTimeout(function(){__ez.fads.RunMultiAuction(id,num,attempt+1);},100);return;}
                    maxRetries=parseInt(maxRetries)||3;if(maxRetries<num){num=maxRetries;}
                    this.log("RUNNING MULTI AUCTION FOR",id);this.multiAuctionsRun.push(id);var duplicatedIds=[];var floor=this.getRawSlotValue(id,'br1');if(typeof floor=='string'){floor=parseFloat(floor);}
                    this.createMultiAuctionFloors(id,floor,maxRetries);for(let i=1;i<=num;i++){var newFloor=this.getNextMultiAuctionFloor(id);var newId=this.duplicateAdPlacement(id,true,null,newFloor);if(typeof newId=='string'){duplicatedIds.push(newId);(function(newId){setTimeout(function(){__ez.fads.loadMultiAd(newId);},i*800);})(newId);}}
                    return duplicatedIds;},getCumulativeDecreaseAmount:function(num,max,start=1){if(num>max){num=max;}
                    return num/max;var total=1;for(var i=start;i<=num;i++){var decrease=this.getFloorDecreaseAmount(i,max);total=total*(1-decrease);}
                    var totalDecrease=total;return totalDecrease;},getPositionId:function(id){var al=__ez.fads.getRawSlotValue(id,'al');if(typeof al=='string'){al=parseInt(al);}
                    if(!isNaN(al)&&al>=1000){al=al%1000;}else{al=-1;}
                    return al;},AddReplaceOnRefresh:function(id,size){this.replaceOnRefresh[id]=size;},removeReplaceOnRefresh:function(id){delete this.replaceOnRefresh[id];},getReplaceOnRefreshSize:function(id){return this.replaceOnRefresh[id]||null;},getFloorDecreaseAmount:function(num,max){if(num==1){return .5;}else{return(0.8*(num/max))+0.1;}},__ez_fad_add:function(id){if(this.addedDivs.indexOf(id)==-1){this.addedDivs.push(id);}},GetInteractionEvents:function(){return["scroll","mousedown","keydown","touchstart","pointerdown","wheel"];},__ez_fad_rdy:function(){if(document.body!==null&&this.loadingAdsAllowed&&(this.__ez_fad_haspo==false&&this.doc_ht>this.vp_ht||this.__ez_fad_hascp)||document.readyState!="loading"){return true;}else{this.doc_ht=this.__ez_fad_docht();this.vp_ht=this.__ez_fad_vpht();}
                    return false;},log:function(){if(__ez.fads.logEnabled){let args=Array.from(arguments);var a=new Date();args.unshift(`${a.getHours()}:${a.getMinutes()}:${a.getSeconds()}.${a.getMilliseconds()}`);args.unshift('%c🧠 ezjit::');args.splice(1,0,'color: #5FA624;');console.log.apply(console,args);}},logAdStats:function(id,stage){if(!__ez.fads.logEnabled){return;}
                    id=this.getCleanDivId(id);if(Array.isArray(id)){for(var idx=0;idx<id.length;idx++){}
                    return;}
                    try{var slotID=id.replace('div-gpt-ad-','');slotID=slotID.substr(0,slotID.lastIndexOf('-'));var refreshCnt=1;if(window.googletag&&window.googletag.pubads){var matchingSlots=window.googletag.pubads().getSlots().filter(s=>s.getSlotElementId()===id);if(matchingSlots.length>0){refreshCnt=matchingSlots[0].getTargeting('alc')[0]||0;}}
                    __ez.fads.adStats=__ez.fads.adStats||{};__ez.fads.adStats[id]=__ez.fads.adStats[id]||[];while(__ez.fads.adStats[id].length<=refreshCnt-1){__ez.fads.adStats[id].push({});}
                    __ez.fads.adStats[id][refreshCnt-1]=__ez.fads.adStats[id][refreshCnt-1]||{};switch(stage){case 'load':__ez.fads.adStats[id][refreshCnt-1].loadStatsTime=performance.now()/1000;__ez.fads.adStats[id][refreshCnt-1].initialFloor=ezslots_raw.filter(s=>s.tap.includes(slotID))[0].br1/100;break;case 'adloadhb':__ez.fads.adStats[id][refreshCnt-1].hbStatsTime=performance.now()/1000;__ez.fads.adStats[id][refreshCnt-1].bestHBBid=null;if(epbjs.adUnits.filter(a=>a.code==id).length<=0){break;}
                    var bids=__ez.fads.adLoadHB.bidCache.getBidsForAdUnit(epbjs.adUnits.filter(a=>a.code==id)[0]);if(bids!==null||bids.length>0){__ez.fads.adStats[id][refreshCnt-1].bestHBBid=bids.sort((i,j)=>{return j.cpm-i.cpm})[0].cpm;}
                    break;case 'bang':var bang={};bang.bangTime=performance.now()/1000;bang.bangStartingFloor=ezslots_raw.filter(s=>s.tap.includes(slotID))[0].br1/100;__ez.fads.adStats[id][refreshCnt-1].bangs=__ez.fads.adStats[id][refreshCnt-1].bangs||[];__ez.fads.adStats[id][refreshCnt-1].bangs.push(bang);break;case 'filled':__ez.fads.adStats[id][refreshCnt-1].fillTime=performance.now()/1000;__ez.fads.adStats[id][refreshCnt-1].filledFloor=ezslots_raw.filter(s=>s.tap.includes(slotID))[0].br1/100;__ez.fads.adStats[id][refreshCnt-1].filledSSID=_ezim_d[slotID].ssid;break;}}catch(e){}},showScrollAnchor:function(){const anchor=document.getElementById('ezmobfooter');if(anchor==null){return;}
                    Array.from(document.getElementsByClassName('ezmob-footer')).forEach(s=>{s.style.top='0px';});document.body.style.transition='padding-top 0.5s ease-in-out';document.body.style.paddingTop='100px';setTimeout(function(){try{let ids=ezslots_raw.filter(s=>s.tap.includes('medrectangle-2')).map(s=>'div-gpt-ad-'+s.tap.split('-').slice(0,3).join('-')+'-0');window.__ez_fad_floating=window.__ez_fad_floating.concat(ids);this.onScreenDivs=this.onScreenDivs.concat(ids);this.markIdsAsRunGAMOnAuctionComplete(ids);this.loadAdsWaitForExternal(ids);}catch(e){}}.bind(this),750);},getAmazonBidFromHash:function(hash){var self=this;function resolveBid(){return self.callLibrary('amznH2Bid','GetBidForHash',[hash]);}
                    if(this.amznH2Bid.loaded===true){return{then:function(callback){return callback(resolveBid());}};}
                    return{then:function(callback){self.loadLibrary(self.amznH2Bid.url,'amznH2Bid');setTimeout(function(){return callback(resolveBid());},750);}};},};__ez.fads.init();function __ez_hb_render(id){setTimeout(function(){window.top.epbjs.renderAd(document,id);},10);}__ez.fads.adLoadGAM=(__ez.fads.adLoadGAM&&__ez.fads.adLoadGAM.loaded===true)?__ez.fads.adLoadGAM:{loaded:true,floatingAdsShown:false,slotsDone:[],slotKV:{},isInit:false,floatingStyleLoaded:false,bangerName:__ez.fads.bangerName,version:1,refreshTargetingResetParams:{'hb_bidder':'','hb_adid':'','hb_pb':'','hb_opt':'','nam':'','pwtsid':'','pwtbst':'','hb_ssid':'','hb_format':'','hb_rt':'','epb':'','epa':'','epp':'','eps':'','epf':'','amznbid':'','amzniid':'','amznp':'','amznsz':'','r_amznbid':'','r_amzniid':'','r_amznp':'','rbs':''},log:__ez.fads.log,hbAsFloorThreshold:0.75,hbAsFloorCap:10.0,init:function(viewportHeight,documentHeight,version){if(this.isInit){return;}
                    if(version){this.version=version;}else{this.version=__ez.fads.version;}
                    this.isInit=true;if(this.version!=4){this.LoadGPT();}
                    if(this.version==15){this.hbAsFloorThreshold=0.5;}
                    __ez.fads.LibraryLoaded("adLoadGAM");},reset:function(){this.floatingAdsShown=false;this.slotsDone=[];this.slotKV={}},cleanupAdData:function(divId){this.slotsDone=this.slotsDone.filter(function(id){return id!==divId;});},RefreshAds:function(ids){if(!Array.isArray(ids)){ids=[ids];}
                    if(typeof window[this.bangerName]!='undefined'){for(var i=0;i<ids.length;i++){var id=ids[i];var gamId=__ez.fads.getDivTranslation(id);if(__ez.fads.adLoadHB.loaded===true){__ez.fads.adLoadHB.SetAdBid(id);}
                    this.adjustFloor(gamId);this.adjustFloorToExtBid(gamId);window[this.bangerName].RefreshById(gamId);}}else{setTimeout(function(){__ez.fads.adLoadGAM.RefreshAds(ids)},100);}},replaceOpenwrapBid:function(slot,hb_bid){var ow_bid=slot.getTargeting('ow_hb_opt')[0];if(typeof ow_bid==='undefined'||ow_bid===''||isNaN(ow_bid)){return false;}
                    ow_bid=parseFloat(ow_bid);if(ow_bid>hb_bid){var targetingParams=slot.getTargetingMap();for(var key in targetingParams){if(key.startsWith('ow_hb_')){var newKey='hb_'+key.slice(6);slot.setTargeting(newKey,targetingParams[key][0]);}}
                    return ow_bid;}
                    return false;},IncreaseFloor:function(id,multiple){var slot=this.GetSlotById(id);var floor=this.getFloor(slot);var newFloor=floor*multiple;this.setFloor(slot,newFloor);},adjustFloor:function(id){if(__ez.fads.adFilled.loaded!==true){return;}
                    var percentOfMax=1.0;if(this.version==16){percentOfMax=1.0;}else{return;}
                    var maxFloor=__ez.fads.adFilled.GetMaxFloor(id,percentOfMax,2);if(maxFloor!==false){var slot=this.GetSlotById(id);var floor=this.getFloor(slot);if(floor>maxFloor){this.setFloor(slot,maxFloor);}}},adjustFloorToExtBid:function(id){var slot=this.GetSlotById(id);if(slot==null){return;}
                    var hb_bid=slot.getTargeting('hb_opt')[0];if(typeof hb_bid==='undefined'||hb_bid===''||isNaN(hb_bid)){return;}
                    var bidType=slot.getTargeting('hb_bidtype')[0];hb_bid=parseFloat(hb_bid);var floor=this.getFloor(slot);let isForcedBid=false;let hbBidder=slot.getTargeting('hb_bidder')[0];if(hbBidder===new URLSearchParams(window.location.search).get('ez_force_hb_solo')){isForcedBid=true;}
                    if(hb_bid>(floor*this.hbAsFloorThreshold)||hb_bid>this.hbAsFloorCap||isForcedBid){this.setFloor(slot,hb_bid,bidType);}
                    if(window.ezhbonly){this.activateHbTargeting(slot,bidType);}},GetFloorFromId(id){var slot=this.GetSlotById(id);return this.getFloor(slot);},GetBidFromId(id){var slot=this.GetSlotById(id);if(!slot){return 0;}
                    var hb_bid=slot.getTargeting('hb_opt')[0];if(typeof hb_bid==='undefined'||hb_bid===''||isNaN(hb_bid)){return 0;}
                    return parseFloat(hb_bid);},getFloor(slot){if(!slot){return 0;}
                    var floor=parseInt(slot.getTargeting('br1')[0]);if(typeof floor==='undefined'||floor===''||isNaN(floor)){return 0;}
                    return floor/100;},setFloorById(id,floor,bidType){var slot=this.GetSlotById(id);this.setFloor(slot,floor,bidType);},setFloor(slot,floor,bidType){if(!slot){return;}
                    var GAMAccount=slot.getTargeting('ga')[0];var formattedFloor=this.formatBid(floor,GAMAccount);if(formattedFloor==0){formattedFloor=2;}
                    if(window.ezoibfh.hasOwnProperty(formattedFloor)){var bidFloorHash=window.ezoibfh[formattedFloor];}
                    if(typeof bidFloorHash!=='undefined'){slot.setTargeting('br1',formattedFloor);slot.setTargeting('eb_br',bidFloorHash);this.activateHbTargeting(slot,bidType);}},activateHbTargeting:function(slot,bidType){if(bidType=='ow'){slot.setTargeting('pwtbst','1');slot.setTargeting('nam','');}else if(bidType=='hb'){slot.setTargeting('nam','1');slot.setTargeting('pwtbst','');}else{slot.setTargeting('pwtbst','');slot.setTargeting('nam','');}},LoadAd:function(ids,attempt){if(!Array.isArray(ids)){ids=[ids];}
                    ids=ids.filter(id=>{const element=document.getElementById(id);return!(element&&element.querySelector('.adsbygoogle.ezasloaded'));});var idsToDo=[];for(var i=0;i<ids.length;i++){if(this.slotsDone.indexOf(ids[i])==-1){idsToDo.push(ids[i]);}}
                    ids=idsToDo;if(ids.length<1){return;}
                    if(attempt<1){attempt=1;}else if(attempt>100){return;}
                    this.slotsDone=this.slotsDone.concat(ids);googletag.cmd.push(function(){if(googletag.pubadsReady!==true){setTimeout(function(){__ez.fads.adLoadGAM.SlotsNotDone(ids);__ez.fads.adLoadGAM.LoadAd(ids,attempt+1)},100);return;}
                    var gamSlots=[];for(var i=0;i<ids.length;i++){var id=ids[i];var slot=__ez.fads.adLoadGAM.GetSlotById(id);if(slot==null){if(!__ez.fads.initslots.hasOwnProperty(id)){__ez.fads.log("id does not exist in initslots",id);continue;}
                    var slot_id=__ez.fads.initslots[id](5);var slot=window[slot_id];}
                    if(typeof __ez.fads.kvStore[id]!=='undefined'){if(typeof __ez.fads.kvStore[id]['hb_opt']!=='undefined'&&!isNaN(__ez.fads.kvStore[id]['hb_opt'])){var curBid=slot.getTargeting('hb_opt')[0];if(typeof curBid!=='undefined'&&!isNaN(curBid)){if(curBid>__ez.fads.kvStore[id]['hb_opt']){delete __ez.fads.kvStore[id]['hb_opt'];delete __ez.fads.kvStore[id]['hb_adid'];delete __ez.fads.kvStore[id]['hb_bidder'];delete __ez.fads.kvStore[id]['hb_pb'];delete __ez.fads.kvStore[id]['hb_rt'];delete __ez.fads.kvStore[id]['hb_bidtype'];delete __ez.fads.kvStore[id]['hb_ssid'];delete __ez.fads.kvStore[id]['hb_format'];}}}
                    for(var name in __ez.fads.kvStore[id]){if(__ez.fads.kvStore[id].hasOwnProperty(name)){slot.setTargeting(name,__ez.fads.kvStore[id][name]);}}
                    __ez.fads.kvStore[id]={};}
                    if(parseInt(slot.getTargeting('al')[0])%1000==5){__ez.fads.adLoadGAM.showFloatingStyle();}
                    if(!slot.getOutOfPage()&&document.getElementById(slot.getSlotElementId())===null){__ez.fads.log("gam slot element isn't on the page",id,slot.getSlotElementId());setTimeout(function(){__ez.fads.adLoadGAM.SlotsNotDone(id);__ez.fads.adLoadGAM.LoadAd(id,attempt+1)},100);continue;}else{googletag.display(slot.getSlotElementId());if(typeof window.ezoResponsiveSizes!='undefined'&&typeof slot!='undefined'&&slot!=null&&slot.getTargeting('al')[0]!='1005'&&slot.getTargeting('al')[0]!='3005'){var sizeString=__ez.fads.adLoadGAM.buildSlotResponsiveSizes(slot.getSlotElementId());if(sizeString!==''){__ez.fads.adLoadGAM.adjustResponsiveDiv(slot.getSlotElementId());slot.defineSizeMapping(sizeString);}}
                    if(typeof window.__ezWillLoadCnx!=='undefined'&&typeof slot!='undefined'&&slot!=null&&slot.getTargeting('al')[0]=='1039'){__ez.fads.adLoadGAM.loadConnatix(slot);continue;}
                    if(__ez.fads.adLoadHB.loaded===true){__ez.fads.adLoadHB.SetAdBid(id);}
                    __ez.fads.adLoadGAM.setQueuedTargeting(id);if(typeof __ez.fads.initbids[id]==='function'){__ez.fads.initbids[id]();delete __ez.fads.initbids[id];}
                    __ez.fads.adLoadGAM.adjustFloor(id);__ez.fads.adLoadGAM.adjustFloorToExtBid(id);gamSlots.push(slot);}}
                    __ez.fads.log("initial loading gam slots",ids,gamSlots);if(gamSlots.length>0){googletag.pubads().refresh(gamSlots);for(var i=0;i<gamSlots.length;i++){if(typeof __ez!=='undefined'&&typeof __ez.pel!=='undefined'){__ez.pel.Add(gamSlots[i],[(new __ezDotData('fetched',1))]);}else{window.ez_pel_cmd=window.ez_pel_cmd||[];window.ez_pel_cmd.push(function(){__ez.pel.Add(gamSlots[i],[(new __ezDotData('fetched',1))]);});}}}});if(this.floatingAdsShown!==true){setTimeout(window.__ez.fads.loadFloatingAds(0,true),0);}},GetTargeting:function(id,key){var slot=this.GetSlotById(id);if(slot){return slot.getTargeting(key);}
                    return[];},IncrementImpressionCount:function(id){var slot=this.GetSlotById(id);if(slot){var ic=slot.getTargeting('ic')[0];if(typeof ic==='undefined'||ic===''||isNaN(ic)){ic=0;}
                    ic++;slot.setTargeting('ic',ic);}},CopyHBTargeting:function(sourceId,destId){if(!sourceId||!destId){return false;}
                    var sourceSlot=this.GetSlotById(sourceId);if(!sourceSlot){return false;}
                    var targetingMap=sourceSlot.getTargetingMap();if(!targetingMap){return false;}
                    for(var key in targetingMap){if((key.startsWith('hb_')||key.startsWith('amzn')||key.startsWith('ow_hb_'))&&targetingMap[key].length>0){this.SetTargeting(destId,key,targetingMap[key][0]);}}
                    return true;},GetStatSourceId:function(id){var slot=this.GetSlotById(id);if(slot){if(slot.getTargeting('nam')[0]=='1'){return parseInt(slot.getTargeting('hb_ssid')[0]);}else{return 35;}}
                    return 0;},SlotsNotDone:function(ids){for(var i=0;i<ids.length;i++){var index=this.slotsDone.indexOf(ids[i]);if(index>-1){this.slotsDone.splice(index,1);}}},ShowFloatingAds:function(){if(this.floatingAdsShown===true||typeof __ez_fad_floating==='undefined'||__ez_fad_floating.length<1){return;}
                    this.floatingAdsShown=true;this.showFloatingStyle();this.LoadAd(__ez_fad_floating);},showFloatingStyle:function(){if(this.floatingStyleLoaded){return;}
                    this.floatingStyleLoaded=true;var e=document.getElementById('ezmobfooter');if(e!=null){e.classList.add('ezmobtrans');}else{var head=document.head||document.getElementsByTagName('head')[0];var style=document.createElement('style');head.appendChild(style);var css="body > #ezmobfooter{bottom:0px;visibility:visible;}";style.type='text/css';if(style.styleSheet){style.styleSheet.cssText=css;}else{style.appendChild(document.createTextNode(css));}}},loadConnatix:function(slot){window.__ezsbwcmd=window.__ezsbwcmd||[];var sr=[slot];window.__ezsbwcmd.push(function(){if(typeof __ezcnxPlayer==='undefined'||!__ezcnxPlayer.getSize()){googletag.pubads().refresh(sr);}else{__ezcnxPlayer.once('removed',function(){googletag.pubads().refresh(sr);});}});},SetTargeting:function(id,key,value){var slot=this.GetSlotById(id);if(!slot){slot=this.GetSlotById(__ez.fads.getDivTranslation(id));}
                    if(slot){slot.setTargeting(key,value);if(this.slotKV[id]&&this.slotKV[id][key]){delete this.slotKV[id][key];}}else{this.slotKV[id]=this.slotKV[id]||{};this.slotKV[id][key]=value;}},resetTargeting:function(id){var slot=this.GetSlotById(id);if(slot){slot.updateTargetingFromMap(this.refreshTargetingResetParams);}},destroySlot:function(id){var slot=this.GetSlotById(id);if(slot){googletag.destroySlots([slot]);}},setQueuedTargeting:function(id){if(this.slotKV[id]){var slot=this.GetSlotById(id);if(slot){for(var key in this.slotKV[id]){slot.setTargeting(key,this.slotKV[id][key]);}}}},GetSlotById:function(n){if(typeof googletag=='undefined'||typeof googletag.pubads!='function'||typeof googletag.pubads().getSlots!='function'){return;}
                    var gamId=__ez.fads.getDivTranslation(n);var slots=googletag.pubads().getSlots();for(var i=0;i<slots.length;i++){var elId=slots[i].getSlotElementId();if(elId==n||elId==gamId||(elId.startsWith("gpt_unit_")&&(elId.includes(n.replace("div-gpt-ad-","").replace(/-0$/,""))||elId.includes(gamId.replace("div-gpt-ad-","").replace(/-0$/,""))))){return slots[i];}}},getSizes:function(id){if(!id){return[];}
                    var slot=this.GetSlotById(id);if(!slot){return[];}
                    return slot.getSizes();},getMaxSizes:function(id){var maxWidth=0;var maxHeight=0;var slot=this.GetSlotById(id);if(!slot){return[0,0];}
                    if(typeof window.ezoResponsiveSizes!=='undefined'&&window.ezoResponsiveSizes[slot.getSlotElementId()]){var responsiveSizes=window.ezoResponsiveSizes[slot.getSlotElementId()].responsiveSizes;if(responsiveSizes){for(var i=0;i<responsiveSizes.length;i++){var sizeConfig=responsiveSizes[i];if(sizeConfig.sizes){for(var j=0;j<sizeConfig.sizes.length;j++){var size=sizeConfig.sizes[j];if(size[0]>maxWidth){maxWidth=size[0];}
                    if(size[1]>maxHeight){maxHeight=size[1];}}}}}}
                    var slotSizes=slot.getSizes();if(slotSizes){for(var i=0;i<slotSizes.length;i++){var size=slotSizes[i];if(size==='fluid'){continue;}
                    var width,height;if(typeof size.getWidth==='function'){width=size.getWidth();height=size.getHeight();}else if(Array.isArray(size)){width=size[0];height=size[1];}else{continue;}
                    if(width>maxWidth){maxWidth=width;}
                    if(height>maxHeight){maxHeight=height;}}}
                    return[maxWidth,maxHeight];},LoadGPT:function(){var attempts=0;var loadGpt=function(){var scriptTag="script";var src="//securepubads.g.doubleclick.net/tag/js/gpt.js";var script=document.createElement(scriptTag);script.async=true;script.type="text/javascript";script.src=src;script.onerror=function(event){if(attempts>1){return;}
                    setTimeout(loadGpt,500);};script.onload=function(){if(attempts<=1){return;}
                    if(typeof reportEzReqError==='function'){reportEzReqError(src,"gpt.js");}};var head=document.head||document.getElementsByTagName('head')[0];head.appendChild(script);attempts++;};loadGpt();},buildSlotResponsiveSizes:function(domID){var gptSizeMapping=googletag.sizeMapping();if(typeof window.ezoResponsiveSizes==='undefined'||typeof window.ezoResponsiveSizes[domID]==='undefined'){return false}
                    window.ezoResponsiveSizes[domID].responsiveSizes.sort(function(a,b){var largerWidth=a.minWidth>b.minWidth;var equalWidth=a.minWidth===b.minWidth;var largerHeight=a.minHeight>b.minHeight;if(largerWidth)return-1;if(equalWidth&&largerHeight)return-1;return 0;});if(window.ezoResponsiveSizes[domID].responsiveSizes){var hasSizesAtZero=false;for(var sizeIdx=0;sizeIdx<window.ezoResponsiveSizes[domID].responsiveSizes.length;sizeIdx++){var sizeDirective=window.ezoResponsiveSizes[domID].responsiveSizes[sizeIdx];if(sizeDirective.minWidth===0&&sizeDirective.minHeight===0){hasSizesAtZero=true;}
                    gptSizeMapping.addSize([sizeDirective.minWidth,sizeDirective.minHeight],sizeDirective.sizes);}
                    if(!hasSizesAtZero){gptSizeMapping.addSize([0,0],[]);}}else{return false;}
                    var mapping=gptSizeMapping.build();if(mapping.length<1){return false;}else{return mapping;}},adjustResponsiveDiv:function(divID){if(!window.ezoResponsiveSizes||!window.ezoResponsiveSizes[divID]){return;}
                    var adDiv=document.getElementById(divID);if(!adDiv){return;}
                    var fillSize=__ez_get_largest_ad_size(divID);if(fillSize.length===0||fillSize[0]===0||fillSize[1]===0){return;}
                    var orivDivSize=[adDiv.getAttribute('ezaw'),adDiv.getAttribute('ezah')];if(orivDivSize[0]===null||orivDivSize[1]===null){return;}
                    if(fillSize[0]>=orivDivSize[0]){return;}
                    this.gamResponsiveResizeDiv(adDiv,fillSize,false);this.gamResponsiveResizeDiv(adDiv.parentElement,fillSize,true);this.gamResponsiveResizeDiv(adDiv.parentElement.parentElement,fillSize,false);},gamResponsiveResizeDiv:function(adDiv,fillSize,isParent){if(!adDiv){return;}
                    adDiv.style.minWidth=fillSize[0]+'px';adDiv.style.maxWidth="100%";if(isParent){adDiv.style.width=fillSize[0]+'px';}else{adDiv.style.width='';}},getLargestAdSize:function(slotID){var applicableSizes=[];if(ezoResponsiveSizes[slotID]){var clientWidth=document.documentElement.clientWidth;var clientHeight=document.documentElement.clientHeight;var bestResponsiveFit=null;window.ezoResponsiveSizes[slotID].responsiveSizes.forEach(responsiveSize=>{var appliesToViewport=responsiveSize.minWidth<=clientWidth&&responsiveSize.minHeight<=clientHeight;var betterResponsiveFit=bestResponsiveFit===null||responsiveSize.minWidth>bestResponsiveFit.minWidth||(bestResponsiveFit.minWidth==responsiveSize.minWidth&&responsiveSize.minHeight>bestResponsiveFit.minHeight);if(appliesToViewport&&betterResponsiveFit){bestResponsiveFit=responsiveSize;}});if(bestResponsiveFit!==null){applicableSizes=bestResponsiveFit.sizes;}}
                    var largestDims=[0,0];applicableSizes.forEach(size=>{if(size[0]>largestDims[0]){largestDims[0]=size[0];}
                    if(size[1]>largestDims[1]){largestDims[1]=size[1];}});return largestDims;},formatBid:function(e,t){var o=e;var gaTargets=this.getGaTargets();if(o>300){o=300;}
                    if(this.isEzoicGamAccount(t,gaTargets)){o=o<=0?0:o<=0.5?2*Math.floor(100*o/2+0.5):o<=1?10*Math.floor(10*o+0.5):o<=3?20*Math.floor(100*o/20+0.5):o<=10?50*Math.floor(100*o/50+0.5):o<=30?100*Math.floor(100*o/100+0.5):o<=50?200*Math.floor(100*o/200+0.5):o<=120?500*Math.floor(100*o/500+0.5):1000*Math.floor(100*o/1000+0.5);if((o/100>e)&&(o>2)){o-=o<=50?2:o<=100?10:o<=300?20:o<=1000?50:o<=3000?100:o<=5000?500:1000;}}else{o=o<=0?0:o<=1?10*Math.floor(10*o+0.5):o<=3?20*Math.floor(100*o/20+0.5):o<=10?50*Math.floor(100*o/50+0.5):o<=30?100*Math.floor(100*o/100+0.5):o<=50?200*Math.floor(100*o/200+0.5):o<=120?500*Math.floor(100*o/500+0.5):1000*Math.floor(100*o/1000+0.5);if((o/100>e)&&(o>2)){o-=o<=100?10:o<=300?20:o<=1000?50:o<=3000?100:o<=5000?200:o<=12000?500:1000;}}
                    return o;},isEzoicGamAccount:function(accountId,gaTargets){if(accountId==""){return true;}
                    return gaTargets.includes(accountId);},getGaTargets:function(){var DfpToADXMap={"1254144":"ca-pub-****************","*********":"ca-pub-****************","***********":"ca-pub-****************","***********":"ca-pub-****************","***********":"ca-pub-****************","***********":"ca-pub-****************","***********":"ca-pub-****************","***********":"ca-pub-****************","***********":"ca-pub-****************","***********":"ca-pub-****************",};let results=[];for(let value of Object.values(DfpToADXMap)){let lastSeven=value.substring(value.length-7);results.push(lastSeven);}
                    return results;}};__ez.fads.adLoadGAM.init();
                    
                    window.__ez_get_largest_ad_size=function(i){var e=[];if(ezoResponsiveSizes[i]){var n=document.documentElement.clientWidth,t=document.documentElement.clientHeight,o=null;window.ezoResponsiveSizes[i].responsiveSizes.forEach((function(i){var e=i.minWidth<=n&&i.minHeight<=t,m=null===o||i.minWidth>o.minWidth||o.minWidth==i.minWidth&&i.minHeight>o.minHeight;e&&m&&(o=i)})),null!==o&&(e=o.sizes)}var m=[0,0];return e.forEach((function(i){i[0]>m[0]&&(m[0]=i[0]),i[1]>m[1]&&(m[1]=i[1])})),m};
                    
                    
                    var ezasVars = {'cid':'9644339319','pid':'pub-5770515425712095','ssid':44};window.handleResponsiveAdsense=function(t,e){var s=[];(e=e||t.parentNode)&&e.attributes&&e.attributes.ezaw&&e.attributes.ezah&&(s=[e.attributes.ezaw.value,e.attributes.ezah.value]);var i=t.id.replace("-asloaded","");if(window.ezoResponsiveSizes&&window.ezoResponsiveSizes[i]&&window.__ez_get_largest_ad_size&&e){s=window.__ez_get_largest_ad_size(i),e.style.cssText+="width: "+s[0]+"px !important",e.style.cssText+="max-width: "+s[0]+"px !important",e.style.cssText+="min-width: 0px !important";var a=e.parentNode;a&&a.classList.contains("ezoic-ad")&&(a.style.cssText+="width: "+s[0]+"px !important",a.style.cssText+="max-width: "+s[0]+"px !important",a.style.cssText+="min-width: 0px !important")}t.style.cssText+="width: "+s[0]+"px",t.style.cssText+="height: "+s[1]+"px"};if(typeof window.ezAutoAdsSetup == 'undefined'){window.google_reactive_ads_global_state = {
                                    adCount: {},
                                    floatingAdsStacking: { maxZIndexListeners: [], maxZIndexRestrictions: {}, nextRestrictionId: 0 },
                                    messageValidationEnabled: false,
                                    reactiveTypeDisabledByPublisher: {},
                                    reactiveTypeEnabledInAsfe: {},
                                    sideRailAvailableSpace: [],
                                    sideRailOverlappableElements: [],
                                    stateForType: {},
                                    tagSpecificState: {},
                                    wasPlaTagProcessed: true,
                                    wasReactiveAdConfigReceived: { 1: true, 2: true, 8: true },
                                    wasReactiveAdVisible: {},
                                    wasReactiveTagRequestSent: true,
                                    description: "Can't disable auto ads programmatically on the page, so here we are!"
                                };};var __ezasAggressive=true;
                    
                    
                    
                    _ebcids=[138231308856,138231308940,138231308949,138231387842,138231421744,138231421759,138231421774,138231421783,138231421789,138231421792,138242067587,138242067590,138242067602,138242067605,138242067608,138242067614,138242229406,138242229415,138242229421,138242229430];
                    
                    
                    
                    
                    window.ezdupads = true;window.ezdupadsblacklist = '';window.isEZABL=false;window.ezmadspc=300;window.ezoViewCheck=false;
                    
                    window.ezDisableInitialLoad=false;
                    window.googletag=window.googletag||{};googletag.cmd=googletag.cmd||[];
                    
                    
                    
                    window.ezogetbrkey = function(s){ var k = 'br1';var k2 = 'eb_br';if(window.ezogtk == ""){k='br1u';k2='eb_bru';}else if(window.ezogtk != "NT"){k='br1t';k2='eb_brt';} s.setTargeting('br1', s.getTargeting(k));s.setTargeting('eb_br', s.getTargeting(k2));};googletag.cmd.push(function() {window.ezslot_init_interstitial = function(){window.ezslot_interstitial = googletag.defineOutOfPageSlot('/***********,22569517314/plantuml_com-pixel1',googletag.enums.OutOfPageFormat.INTERSTITIAL); if(window.ezslot_interstitial != null) {window.ezslot_interstitial.addService(googletag.pubads()).setTargeting('eb_br','f302f372ff71e74e10e43891c8c268c4').setTargeting('br1','400').setTargeting('br2','160').setTargeting('ga','3200523').setTargeting('iid1','9015053164625143').setTargeting('tap','plantuml_com-pixel1-9015053164625143').setTargeting('bv','5').setTargeting('bvm','0').setTargeting('bvr','7').setTargeting('bra','mod275-c').setTargeting('ap','9999').setTargeting('al','1006').setTargeting('ic','1').setTargeting('ezoic','1').setTargeting('d','173770').setTargeting('reft','n').setTargeting('avc','166');googletag.pubads().refresh([window.ezslot_interstitial]);}};
                                googletag.pubads().enableSingleRequest();googletag.pubads().addEventListener('slotRenderEnded', function(event) { __ez.queue.addFunc("ezbanger", "ezbanger", event, false, ['banger.js'], true, true, false, true); });googletag.pubads().addEventListener('impressionViewable', function(event) { __ez.queue.addFunc("ezvb", "ezvb", event, false, ['banger.js'], true, true, false, true); });googletag.pubads().addEventListener('slotVisibilityChanged', function(event) { __ez.queue.addFunc("ezvt", "ezvt", event, false, ['banger.js'], true, true, false, true); });googletag.pubads().addEventListener('slotResponseReceived', function(event) { __ez.queue.addFunc("ezsr", "ezsr", event, false, ['banger.js'], true, true, false, true); });googletag.pubads().addEventListener('slotRequested', function(e) { window.ezsrqt[e.slot.getSlotElementId()] = Date.now();});googletag.setConfig({adExpansion: {enabled: true}});googletag.pubads().disableInitialLoad();googletag.pubads().enableLazyLoad({fetchMarginPercent: 700, renderMarginPercent: 10, mobileScaling: 1.0});var ezppid = document.cookie.split('; ').reduce((val, cookie) => val || (cookie.startsWith('ezppid_ck=') ? cookie.split('=')[1] : null), null); if (ezppid) googletag.pubads().setPublisherProvidedId(ezppid);googletag.enableServices();});window.gamAnchorPosition = 'bottom';window.ezocolaver = 3;window.ezoadxnc = '***********';window.ezoadhb = '12';var ezoibfh = {0:'zero',1000000:'off',10000:'32acbf5003be628145d61ae2a63693d8',1000:'7a6282946fbff71422dea4ca3bc93eb0',100:'56fc713801c7ed639d3ac204fd41e080',10500:'2a2e1a401d10dced12422d6891c90e3d',10:'bf77e989084343ea657c1f95a86f3c39',11000:'c1cb8b7fa1a2b7c91d62dc75165c4c0d',1100:'3d1414733d321d5212f0767011cff2a3',11500:'9efe3cfc2da12443b51a299bb8e6c331',12000:'770e522aad61a9919eab147e99d066df',1200:'7198202cebb76cb25756946c690ca5da',120:'38497ca860f9f404b0bee3685c19e8c8',12:'3d3e2d41044969bc7ab50ac6fc7fdbd2',13000:'cbfb2f53199a9299211f3e34c028789e',1300:'a1b026f4b96d46f5fe81e04bc73394de',14000:'771c8f2de11dd23d58eae8b0270bdb20',1400:'45f1c6bcbe87b40a6c74939dd3e95a3e',140:'d70e254d07e366032effd70c7d8e972e',14:'1cf0927e5adf45aaf437946f249367f5',15000:'ecdba46da90015ab4c002fe45b42e296',1500:'7a8acfee05a9fc11382866202fb3b606',16000:'141e06e90ad634c2c57d2410554a238f',1600:'3540cea665d144d313e517a343562719',160:'b23a29320d062c91d635fd03c1beb92e',16:'31dc420a1d8925b16ef1c3dfa20c3750',17000:'2001b9cee7085c62025c8829d5e14264',1700:'c4b665f314d236690b68e81e9cbcb49f',18000:'2a9e829565f4d38f18d7150e3e4b7c3a',1800:'3bbedcf7f53cce34b8cc5f0473d753bb',180:'e38864b54f13626c2ba008926a7dd0ea',18:'5c4652790a3016f30fbcfc3194e99d94',19000:'7eb8d77085bd5f43b07e35d83ec63df5',1900:'c11da5e4c87874bb7ec3fa430d5d0c8f',20000:'a0ad6542ec5aa6d04e3e3422b0cec558',2000:'26322a234afdb8da97feab43bcd58244',200:'21499fc5aeeb0aabd97ca3354b35f0d5',20:'5b062d723603a551179fa70348541130',21000:'c53b5ed573b45a58c4de40c6ff29a0f3',2100:'13f73647e2935ce889599d7ed7a330c9',22000:'63de5ca49ca51052f0bb3bb656d4e1f2',2200:'35ccb7c8b93414e21c885bbc8c3a4090',220:'cf9ebb43d2cdc6e7af0d95f25ea2ae5e',22:'a313bac43fb226b6b59382251beb6934',23000:'75ed79b66cfaa6b69dae626648c4ef70',2300:'033489d06aea35be06c256d365fa8c6b',24000:'309cb25086a26fafbfaba1d3ccfa9633',2400:'a24401534770f5cc7b3951a287c95465',240:'3395edced27e5bcb1a00bbeba1a51698',24:'c899e55d1b8e50b2e8b621f6e84e77b5',25000:'2277bcea9a555fbc401c7003f9cad643',2500:'c9b8c8bd068efd32d29fcd18a8f9c936',26000:'92307b4d244e793d305a66f1b83f26db',2600:'0d797d3e891975900a045bb83a811adb',260:'58b7475ad6ee97248baac838754dcbc9',26:'a613be9cdd84367241bf87b9c6204a9e',27000:'cd34db8d778ce6fe81b2dc8a075af0fd',2700:'f8b5b511894ed4262f0479cc676f4b48',28000:'fe5c0ef3c25ef3eab9d2b28c53cf1bcd',2800:'33232394f9d8ec8bd82e0aea7947519c',280:'5ad105bfb266b5b11b75f0838c5cd82e',28:'dfa9162f3090043f575a27570fb6b239',29000:'5c2fece85b465872e86278c41151a6df',2900:'15dff6689fd9c401a66e391269625c80',2:'7f82d38739c396178d72647f562ab22f',30000:'98b324cb2916877bd46a328a7ac74f8e',3000:'ade4fc703595c3ba6fd7459638545c7f',300:'5c69ab69f651bb9733e14cbe52e5bf89',30:'6bf306fa315c3a3cd9da641c09fdeaf2',3200:'2b466b7dd2d97cfd44d64162e603ca7c',32:'cdf66cf4c5a15823302811b16009801e',3400:'8a063b1688bb3c9fb95d6cde87f651bf',34:'ed54106ec1d4e963fbb694ef8695294b',350:'c6bc4593775184872e6111bc71c367ac',3600:'e26b67a0abbe23ca19560aa92e7daa3d',36:'0bf2359afa41236ab95e9289ec02b0b9',3800:'240a33cdb640874030254daa92287f5a',38:'0860ad56f31da1087d7e7810d267e5b5',4000:'701e5c88a2c6fcd01bd027a55fb0aab9',400:'f302f372ff71e74e10e43891c8c268c4',40:'b8f8a7b3f4c9663c9b41b8aaa6944f2f',4200:'75a1b4c7996ec829727aa05d16728520',42:'e0f27dd513245624093dd10a696c4a15',4400:'48e633da73e1b345b3703639088e8d03',44:'0169ba5910c422f3f5ad4da95c89228b',450:'af6b638cbfd4228d468f118162a35e63',4600:'3a0cdc390c772c43e1092d187f1fbe75',46:'a27391e2c47e6d5e959d7bc602ac3473',4800:'0e7beb660e7f32244b9aef205d0da21c',48:'b5aa75f595679059fe953485fa463d6a',4:'5109bee04c5f3d18cc9e3e0bee30eddc',5000:'fc8a8b76d4af3258e1247d843d3d8ed5',500:'67bd31cf93fba8a10926473f1c255df5',50:'a79125351ea473b01f82e0233457ef34',5500:'4c9f3f67a074947f2ca107b91d03a88a',550:'3fc660fbb3342ce686fe24301728d7d1',6000:'a60fe0bd2348ded47548c50062b02a4f',600:'1fdbae3c4e3e2e1f9455a434a72b1392',60:'83bc90577326f8e31b21efba57533c52',6500:'16d0a559acab285fa7b4d53c59e0b2a5',650:'59ea894f906f6826bd15853ee61caaad',6:'0f778d24b32619c36ff52a7d5d5ac5db',7000:'6e3b5faeab99902cebd872f75a4fa4d2',700:'8441ce785d1c63b5bb9faee37733421c',70:'f76b4d9d7f07d1627af67b4665a4d2ea',7500:'0c4ea32a862b50b7500350d4964a3340',750:'b06a76590a7ba4e2d31e35c7192c287a',8000:'21bcdb01e833ad19623a38ab278219ec',800:'abadc8b1202c4ca75dcadd3c7b80260b',80:'17d081f8a81ae43a1ba50ebc6433152e',8500:'273a66307d479106f94ed13f98c97f16',850:'fc6db6d2ab27c51b7440e4888584b40d',8:'bddef096b6ce027f589171043802e0be',9000:'5582691c9ca38e41b5286a9f322b54ed',900:'1b1fa0024147ba185dd3f1ca4aa7ea77',90:'99bb37b22218785dde2bf2ad9be213b7',9500:'c2ef1881bd1fdb58f311003797dfeee4',950:'9cda5a7b159bc10cf65ae28886b60455'};var ezaxmns={};var ezaucmns={};ezaucmns["/***********,22569517314/plantuml_com-pixel1"]=42;
                    var __ez_fad_floating = ['div-gpt-ad-plantuml_com-medrectangle-2-0'];if(typeof __ez.fads != 'undefined' && typeof __ez.fads.FloatingAdded == "function"){__ez.fads.FloatingAdded();}
                        function __ez_init_slot(bvr, did, slotNum, defineFunc) {
                            googletag.cmd.push(function() {
                                defineFunc();
                                ezrpos[slotNum]=slotNum;
                                ezslots.push("ezslot_" + slotNum);
                                if(__ez.fads.kvStore[did] && typeof __ez.fads.kvStore[did] === 'object') {
                                    var keys = Object.keys(__ez.fads.kvStore[did]);
                                
                                    keys.forEach(function(name) {
                                        ezSetSlotTargeting(did, name, __ez.fads.kvStore[did][name]);
                                    });
                                }
                            });return "ezslot_" + slotNum;
                        }
                    var ezslot_1_raw = {'a':'1','iid1':'4030693584591501','eid':'4030693584591501','t':'134','d':'173770','t1':'134','pvc':'0','url':'https://editor.plantuml.com/uml/','path':'/uml/','ap':'1676','sap':'1676','as':'revenue','plat':'1','bra':'mod275-c','ic':'1','at':'mbf','adr':'399','ezosn':'1','reft':'tf','refs':'30','refa':'0','ga':'3200523','gala':'','rid':'99998','pt':'34','al':'1034','compid':'0','dfpsn':'/***********,22569517314/plantuml_com-large-billboard-2','tap':'plantuml_com-large-billboard-2-4030693584591501','eb_br':'56c142c76ce57a5a3baacbfef3a10072','eba':'1','ebss':[10017,10082,10061,10063,11307,11291,11315,11296],'asau':'2835240966','bv':'11','bvm':'0','bvr':'1','avc':'16','shp':'2','ftsn':'12','ftsng':'12','br1':'0','br2':'0','ezoic':'1','nmau':'0','mau':'0','stl':[77,168,0,0,0,193,132,20,0,30,192,31,902,903,901,902,903],'deal1':[17,18,19,20,21,22,23,24,25,26,919,1428,1794,2310,2339,2351,2526,2527,2610,2688,2693,2761,2763,2764,2765,3044,3045,3052,3053,3054,3154,3430,3455,3456,3457,3458,3460,3682,3683,3684,3856,3919,3933,4184,4185,4186,4276,4604,4605,5747,6044,6293,6294,6295,6983,7035,7036,7060,7144,7327,7330,7331,7605,7606,7607,7608,7609,7610,7611,7612,7613,7614,7615,7616,7617,7618,7788,7789,7790,2030,4312,6772,3676,4467,5534,7661,774,7713],'ax_ssid':'10082'}; window.ezslots_raw.push(ezslot_1_raw); window.ezslotdivs['div-gpt-ad-plantuml_com-large-billboard-2-0'] = {slot:'ezslot_1',adunit:'/***********,22569517314/plantuml_com-large-billboard-2'};__ez.fads.initslots['div-gpt-ad-plantuml_com-large-billboard-2-0'] = function(bvr){var defScript = function() {ezslot_1 = googletag.defineSlot('/***********,22569517314/plantuml_com-large-billboard-2',[160,600],'div-gpt-ad-plantuml_com-large-billboard-2-0').addService(googletag.pubads()).setCollapseEmptyDiv(false);ezSetTargetingFromMap(ezslot_1,ezslot_1_raw);};return __ez_init_slot(bvr,'div-gpt-ad-plantuml_com-large-billboard-2-0',1, defScript);};
                    var ezslot_2_raw = {'a':'1','iid1':'8795704134650751','eid':'8795704134650751','t':'134','d':'173770','t1':'134','pvc':'0','url':'https://editor.plantuml.com/uml/','path':'/uml/','ap':'1100','sap':'1100','as':'revenue','plat':'1','bra':'mod275-c','ic':'1','at':'mbf','adr':'399','ezosn':'2','reft':'tf','refs':'30','refa':'0','ga':'3200523','gala':'','rid':'99998','pt':'5','al':'1005','compid':'0','dfpsn':'/***********,22569517314/plantuml_com-medrectangle-2','tap':'plantuml_com-medrectangle-2-8795704134650751','eb_br':'56c142c76ce57a5a3baacbfef3a10072','eba':'1','ebss':[10017,10082,10061,10063,11307,11291,11315,11296],'asau':'2835240966','bv':'24','bvm':'0','bvr':'2','avc':'36','shp':'1','ftsn':'12','ftsng':'12','br1':'0','br2':'0','ezoic':'1','nmau':'0','mau':'0','ganc':'1','cal':'2','stl':[32,193,0,0,0,193,132,20,0,0,192,31,902,903,901,902,903],'deal1':[17,18,19,20,21,22,23,24,25,26,27,28,29,30,760,761,813,815,816,817,818,899,919,1428,1794,2310,2339,2351,2526,2527,2610,2688,2693,2761,2763,2764,2765,3044,3045,3052,3053,3054,3154,3430,3455,3456,3457,3458,3460,3682,3683,3684,3856,3933,4184,4186,4276,4604,4605,5747,6293,6294,6295,6983,7035,7036,7330,7331,7605,7606,7607,7608,7609,7610,7611,7612,7613,7614,7615,7616,7617,7618,7788,7789,7790,2030,4312,6772,3676,4467,5534,7661,774,7713],'ax_ssid':'10082'}; window.ezslots_raw.push(ezslot_2_raw); window.ezslotdivs['div-gpt-ad-plantuml_com-medrectangle-2-0'] = {slot:'ezslot_2',adunit:'/***********,22569517314/plantuml_com-medrectangle-2'};__ez.fads.initslots['div-gpt-ad-plantuml_com-medrectangle-2-0'] = function(bvr){var defScript = function() {ezslot_2 = googletag.defineOutOfPageSlot('/***********,22569517314/plantuml_com-medrectangle-2',googletag.enums.OutOfPageFormat.BOTTOM_ANCHOR).addService(googletag.pubads());ezSetTargetingFromMap(ezslot_2,ezslot_2_raw);};return __ez_init_slot(bvr,'div-gpt-ad-plantuml_com-medrectangle-2-0',2, defScript);};
                    var ezslot_0_raw = {'a':'1','iid1':'8966566860617551','eid':'8966566860617551','t':'134','d':'173770','t1':'134','pvc':'0','url':'https://editor.plantuml.com/uml/','path':'/uml/','ap':'1012','sap':'1012','as':'revenue','plat':'1','bra':'mod275-c','ic':'1','at':'mbf','adr':'399','ezosn':'0','reft':'tf','refs':'30','refa':'0','ga':'3200523','gala':'','rid':'99998','pt':'12','al':'1012','compid':'0','dfpsn':'/***********,22569517314/plantuml_com-square-1','tap':'plantuml_com-square-1-8966566860617551','eb_br':'3d3e2d41044969bc7ab50ac6fc7fdbd2','eba':'1','ebss':[10017,10082,10061,10063,11307,11291,11315,11296],'asau':'2835240966','bv':'11','bvm':'0','bvr':'1','avc':'26','shp':'1','ftsn':'12','ftsng':'12','br1':'12','br2':'6','ezoic':'1','nmau':'0','mau':'0','stl':[63,14,28,4,51,0,88,0,71,30,0,31,901,902,903],'deal1':[17,18,19,20,21,22,23,24,25,26,1428,1794,2310,2339,2351,2526,2527,2610,2688,2693,2761,2763,2764,2765,3044,3045,3052,3053,3054,3154,3430,3455,3456,3457,3458,3460,3682,3683,3684,3856,4184,4186,4276,5747,6293,6294,6295,7035,7036,7330,7331,7605,7606,7607,7608,7609,7610,7611,7612,7613,7614,7615,7616,7617,7618,7788,7789,7790,2030,4312,6772,3676,4467,5534,7661,774,7713],'ax_ssid':'10082'}; window.ezslots_raw.push(ezslot_0_raw); window.ezslotdivs['div-gpt-ad-plantuml_com-square-1-0'] = {slot:'ezslot_0',adunit:'/***********,22569517314/plantuml_com-square-1'};__ez.fads.initslots['div-gpt-ad-plantuml_com-square-1-0'] = function(bvr){var defScript = function() {ezslot_0 = googletag.defineOutOfPageSlot('/***********,22569517314/plantuml_com-square-1',googletag.enums.OutOfPageFormat.REWARDED).addService(googletag.pubads());ezSetTargetingFromMap(ezslot_0,ezslot_0_raw);};return __ez_init_slot(bvr,'div-gpt-ad-plantuml_com-square-1-0',0, defScript);};
                    
                    function __ez_fad_ezpbinit(){__ez.queue.addFileOnce('dall.js', '//go.ezodn.com/hb/dall.js?cb=195-2-122', false, [], true, false, true, false);}__ez.queue.addFileOnce('/detroitchicago/tuscon.js', '/detroitchicago/tuscon.js?gcb=2&cb=14', true, [], true, false, true, false);__ez.queue.addFileOnce('/detroitchicago/kenai.js', '/detroitchicago/kenai.js?gcb=2&cb=3f9035f7d9', true, [], true, false, true, false);var ezCriteo={bidder:'criteo',params:{networkId: 7987, pubid: '101496'}};;var ezjsps=function(obj){return JSON.parse(JSON.stringify(obj));};var epbjs=epbjs||{};epbjs.que=epbjs.que||[];epbjs.bidderTimeout=1000;epbjs.useAdj=true;epbjs.SS={"criteo":10050,"ix":10082,"rubicon":10063,"sharethrough":11309,"smartadserver":11335,"sovrn":10017,"triplelift":11296,"ttd":11384,"yieldmo":11315};epbjs.bidders=['criteo','ix','rubicon','sharethrough','smartadserver','sovrn','triplelift','ttd','yieldmo'];epbjs.que.push(function(){});epbjs.bidderSettings={'triplelift': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'criteo': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'rubicon': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'ttd': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'ix': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'sharethrough': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'smartadserver': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'sovrn': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}},'yieldmo': { bidCpmAdjustment: function(bidCpm, bid) { var adjBid = 0; if(typeof bid === 'undefined'){return bidCpm;} if(bid.mediaType === 'video') { if (bid && bid.adUnitCode && !bid.adUnitCode.includes('outstream')){var reduction = 0.18; if(typeof __ez !== 'undefined' && typeof __ez.tf !== 'undefined' && __ez.tf.vaitp === 'true') reduction = 0; adjBid = bidCpm - reduction;}else{adjBid = bidCpm;}}else{adjBid = bidCpm - 0.015;} if(adjBid < 0){adjBid=0;} return adjBid;}}};epbjs.gadj=1.000000;var __enableAnalytics=false;epbjs.testFeatures=[''];
                    var __s2sbidders=[];
                    var __s2sinstreambidders=[];
                    var __s2soutstreambidders=[];
                    var __allBidders=['criteo','ix','rubicon','sharethrough','smartadserver','sovrn','triplelift','ttd','yieldmo'];
                    var __allSiteApprovedBidders=['amx','appnexus','cadent_aperture_mx','criteo','gumgum','inmobi','insticator','ix','medianet','minutemedia','onetag','pubmatic','rubicon','sharethrough','smartadserver','sonobi','sovrn','triplelift','ttd','vidazoo','yieldmo'];
                    var __ezExtraAuctionExcludedBidders=['criteo','ix','oftmedia','ttd','triplelift','unruly'];
                    __ez.queue.addFileOnce('/detroitchicago/portland.js', '/detroitchicago/portland.js?gcb=2&cb=a2ac4ddbb3', true, [], true, false, true, false);var epbjs=epbjs||{};epbjs.ezAdUnits=[{bidPoolId: 'Sidebar_BPID',code: 'div-gpt-ad-plantuml_com-large-billboard-2-0', ortb2Imp: {ext: {gpid: 'div-gpt-ad-plantuml_com-large-billboard-2-0', data: {pbadslot: 'div-gpt-ad-plantuml_com-large-billboard-2-0'}}}, mediaTypes: {banner: { sizes:[[160,600]] }}, bids: [{bidder: 'triplelift', params: { inventoryCode: 'Ezoic_RON_SidebarMiddle'}},ezjsps(ezCriteo),{bidder: 'rubicon', params:{ accountId: 21150, siteId: 269072, zoneId: 3326304, bidonmultiformat: true  }},{bidder: 'ttd', params:{ supplySourceId: 'ezoic', publisherId: '1dbb067e4138c8d0cfe36f9c3b751a1f'}}], tfl: [] },{bidPoolId: 'Content_BPID_Lazy',code: 'div-gpt-ad-plantuml_com-medrectangle-2-0', ortb2Imp: {ext: {gpid: 'div-gpt-ad-plantuml_com-medrectangle-2-0', data: {pbadslot: 'div-gpt-ad-plantuml_com-medrectangle-2-0'}}}, mediaTypes: {banner: { sizes:[[728,90],[970,90]] }}, bids: [ezjsps(ezCriteo),{bidder: 'rubicon', params:{ accountId: 21150, siteId: 269072, zoneId: 3326304, bidonmultiformat: true  }},{bidder: 'ttd', params:{ supplySourceId: 'ezoic', publisherId: '1dbb067e4138c8d0cfe36f9c3b751a1f'}}], tfl: [] },{bidPoolId: 'Content_BPID_Lazy',code: 'div-gpt-ad-plantuml_com-square-1-0', ortb2Imp: {ext: {gpid: 'div-gpt-ad-plantuml_com-square-1-0', data: {pbadslot: 'div-gpt-ad-plantuml_com-square-1-0'}}}, mediaTypes: {banner: { sizes:[[1,1]] }}, bids: [ezjsps(ezCriteo),{bidder: 'rubicon', params:{ accountId: 21150, siteId: 269072, zoneId: 3326304, bidonmultiformat: true  }},{bidder: 'ttd', params:{ supplySourceId: 'ezoic', publisherId: '1dbb067e4138c8d0cfe36f9c3b751a1f'}}], tfl: [] }];var ez__id5pd = 'MTA9MTk3LjI0NC40MC4yMDUmMTI9TW96aWxsYSUyRjUuMCslMjhXaW5kb3dzK05UKzEwLjAlM0IrV2luNjQlM0IreDY0JTI5K0FwcGxlV2ViS2l0JTJGNTM3LjM2KyUyOEtIVE1MJTJDK2xpa2UrR2Vja28lMjkrQ2hyb21lJTJGMTM2LjAuMC4wK1NhZmFyaSUyRjUzNy4zNg==';var ez__uIdHash = '111673fe8047109fec533c21a5d768e93f49d919d9a4229f2dcde8b37768d965';var ez__sspDomain = 'editor.plantuml.com';var epbjs=epbjs||{};epbjs.que=epbjs.que||[];epbjs.que.push(function(){epbjs.setConfig({})});
                    
                    
                        var __advertiserRule=[''];
                    
                    
                    
                    (function() {
                      var s = document.createElement('script');
                      s.setAttribute('src', '/detroitchicago/augusta.js?cb=49');
                      s.setAttribute('async', 'true');
                      s.setAttribute('data-ezscrex', 'false');
                      document.body.appendChild(s);
                    })();
                    
                    
                    
                    
                    
                    
                    
                    
                    var ezAardvarkDetected;function ezDetectAardvark(){var bait=document.createElement("div");bait.className="textads banner-ads banner_ads ad-unit ad-zone ad-space adsbox";bait.style.height="1px";document.body.appendChild(bait);var baitOffsetHeight=bait.offsetHeight;if(typeof window["_ezaq"]!=="undefined"){if(baitOffsetHeight){ezAardvarkDetected=false;__ez&&__ez.bit&&__ez.bit.Add(window["_ezaq"]["page_view_id"],[(new __ezDotData('is_ad_blocked',false))]);}
                    else{ezAardvarkDetected=true;__ez&&__ez.bit&&__ez.bit.Add(window["_ezaq"]["page_view_id"],[(new __ezDotData('is_ad_blocked',true))]);}
                    var observer=new MutationObserver(function(e){if(e[0].removedNodes){ezAardvarkDetected=true;__ez&&__ez.bit&&__ez.bit.Add(window["_ezaq"]["page_view_id"],[(new __ezDotData('is_ad_blocked',true))]);}});}
                    if(typeof observer!=='undefined'){observer.observe(bait,{childList:true,attributes:true});}}
                    window.addEventListener('load',ezDetectAardvark);
                    
                    __ez.queue.addFile('banger.js', '/porpoiseant/banger.js?cb=195-2&bv=447&PageSpeed=off', true, ['ezaqReady'], true, false, false, true);
                    var _ezim_d = {"plantuml_com-large-billboard-2":{"adsense_stat_source_id":5,"adx_ad_count":4,"adx_stat_source_id":10055,"div_id":"div-gpt-ad-plantuml_com-large-billboard-2-0","full_id":"plantuml_com-large-billboard-2/2025-05-31/4030693584591501","height":"600","position_id":1676,"sub_position_id":1676,"width":"160"},"plantuml_com-medrectangle-2":{"adsense_stat_source_id":5,"adx_ad_count":4,"adx_stat_source_id":10055,"div_id":"div-gpt-ad-plantuml_com-medrectangle-2-0","full_id":"plantuml_com-medrectangle-2/2025-05-31/8795704134650751","height":"90","position_id":1100,"sub_position_id":1100,"width":"970"},"plantuml_com-pixel1":{"adsense_stat_source_id":5,"adx_ad_count":4,"adx_stat_source_id":10055,"div_id":"div-gpt-ad-plantuml_com-pixel1-0","full_id":"plantuml_com-pixel1/2025-05-31/9015053164625143","height":"3","position_id":1006,"sub_position_id":1006,"width":"3"},"plantuml_com-square-1":{"adsense_stat_source_id":5,"adx_ad_count":4,"adx_stat_source_id":10055,"div_id":"div-gpt-ad-plantuml_com-square-1-0","full_id":"plantuml_com-square-1/2025-05-31/8966566860617551","height":"1","position_id":1012,"sub_position_id":1012,"width":"1"}};
                    __ez.fads = __ez.fads || {}; __ez.fads.cmd = __ez.fads.cmd || []; __ez.fads.cmd.push(function () {__ez.fads.loadLibrary('ezadloadrewarded.js', 'adLoadRewarded')});window.__ez.fads.adLoadRewarded = window.__ez.fads.adLoadRewarded || {};window.__ez.fads.adLoadRewarded.config = {"content_blocker":false,"content_blocker_accept_text":"Watch Ad","content_blocker_body":[""],"content_blocker_cooldown":24,"content_blocker_cooldown_message":"You will be able to continue browsing for %cooldown after watching the ad.","content_blocker_header":"Watch Ad to Continue Browsing","content_blocker_min_pageviews":3,"floor_drop_coeff":1,"max_ad_load_retries":1,"min_fill_floor":0,"min_initial_floor":0,"preload_initial":false,"preload_refresh":false};
                    var ezS = document.createElement("script");ezS.src="/detroitchicago/reportads.js?gcb=195-2&cb=5";document.head.appendChild(ezS);
                    window.ezAnchorDisableBodyPadding=false;
                    window.ezAnchorPosition='bottom';
                    var ezS = document.createElement("style");ezS.innerHTML=".ezoic-ad.large-billboard-2676{align-items:center;box-sizing:content-box;display:flex !important;flex-direction:column !important;float:none !important;justify-content:center;line-height:0px;margin-left:0px !important;margin-right:0px !important;max-width:100% !important;min-height:600px;min-width:160px;padding:0;}";document.head.appendChild(ezS);
                    var ezS = document.createElement("style");ezS.innerHTML=".ezoic-ad.medrectangle-2100{box-sizing:content-box;display:inline-block;float:none !important;line-height:0px;max-width:100% !important;min-height:90px;min-width:0px;padding:0;}";document.head.appendChild(ezS);
                    var ezS = document.createElement("style");ezS.innerHTML=".ezoic-ad{display:inline-block;border:0px;}.ezoic-ad>div>iframe{margin:0px!important;padding:0px!important;width:revert-layer;}.ezoic-ad .ezoic-ad>div{text-align:center}";document.head.appendChild(ezS);
                    var ezS = document.createElement("style");ezS.innerHTML=".ad-reporter-menu-backdrop{display:none;position:absolute;bottom:20px;right:15px;width:120px;height:100px;flex-direction:row;justify-content:center;align-items:center;background:transparent;box-shadow:#000 0 2px 10px;border-radius:10px;z-index:1002}.ad-reporter-menu{position:absolute;z-index:1003;display:flex;flex-direction:column;width:100%;height:100%;background:#fff;border-radius:5px;align-items:center;justify-content:space-around;font-weight:200;font-size:14px;font-family:sans-serif}.ad-reporter-menu .ad-reporter-menu-item{width:100%;height:100%;cursor:pointer;display:flex;justify-content:center;align-items:center}.ad-reporter-menu .ad-reporter-menu-item:not(:last-child){border-bottom:solid #d3d3d3 1px}";document.head.appendChild(ezS);
                    var ezS = document.createElement("style");ezS.innerHTML="@keyframes ezIn { 			from { opacity: 0; } 		  }		  .ezoic-ad .ezoic-adl:before {content: '\\00B7\\00B7\\00B7';position: absolute;display: flex!important;align-items: center;justify-content: center;text-align: center;color: #C4C4C4;font-size: 62px;letter-spacing: 2px;z-index: 0;animation: ezIn 1s infinite alternate;left: 50%;top: 50%;transform: translate(-50%, -50%);} .ezoic-ad .ezfound,.ezmob-footer .ezoic-ad .ezoic-ad,.ezoic-ad-adaptive > .ezoic-ad, .ezoic-ad-rl {background:0 0;border-radius:0;border:none}";document.head.appendChild(ezS);
                    var ezS = document.createElement("style");ezS.innerHTML=".ezmob-footer{position:fixed;left:0;bottom:0;width:100%;background:#fff;z-index:100000;line-height:0}.ezmob-footer-desktop{background-color:#f6f6f6cc;white-space:nowrap}.ezmob-footer-close-wrap{height:90px;padding:3px 0 0 16px;display:inline-block;width:35px;vertical-align:top}span.ezmob-footer-close{cursor:pointer;display:inline-block;width:18px;height:18px;border-radius:50%;border:1px solid #696969;position:relative;background:#fff;opacity:1}span.ezmob-footer-close:hover{border:1px solid #5fa624}span.ezmob-footer-close::before,span.ezmob-footer-close::after{content:'';position:absolute;top:50%;left:50%;width:60%;height:1px;background:#696969}span.ezmob-footer-close::before{transform:translate(-50%,-50%) rotate(45deg)}span.ezmob-footer-close::after{transform:translate(-50%,-50%) rotate(-45deg)}img.ezmob-anchor-img{height:18px!important;padding:0!important;border:0!important;cursor:pointer!important;width:18px!important;margin:45px 0 3px 1px!important;box-sizing:content-box!important}@media(min-width:450px) and (max-width:780px){.ezmob-footer-close-wrap,.ezoicwhat{display:none!important}}.ezoicwhat{display:block}body {					padding-bottom: 100px !important;					height: auto;				}				";document.head.appendChild(ezS);
                    ezStaticAnchor('//*/span[@data-ez-ph-id="676"]', '<span class="ezoic-ad ezoic-at-0 large-billboard-2 large-billboard-2676 adtester-container adtester-container-676" data-ez-name="plantuml_com-large-billboard-2"><span id="div-gpt-ad-plantuml_com-large-billboard-2-0" ezaw="160" ezah="600" style="position:relative;z-index:0;display:inline-block;padding:0;min-height:600px;min-width:160px;" class="ezoic-ad ezoic-adl"><script data-ezscrex="false" data-cfasync="false" type="text/javascript" style="display:none;">if(typeof ez_ad_units == "undefined"){ez_ad_units=[];}ez_ad_units.push([[160,600],"plantuml_com-large-billboard-2","ezslot_1",676,"0","0", "plantuml_com-large-billboard-2-0"]);if(typeof __ez_fad_position == "function"){__ez_fad_position("div-gpt-ad-plantuml_com-large-billboard-2-0");}</sc' + 'ript></span><span data-nosnippet="" style="display:block;height:14px;margin:2px auto;position:relative; width:100%" class="reportline" data-ez-ph-owner-id="676"><span style="text-align:center;font-size:12px !important;font-family: arial!important;float:right;line-height:normal;" class="ezoicwhat"><img src="https://go.ezodn.com/utilcave_com/ezoicbwa.png" alt="Ezoic" loading="lazy" style="height:14px !important; padding:2px !important; border:0px !important; cursor:pointer !important; width: 14px !important; margin:0 !important; box-sizing: content-box !important;" title="ezoic" name="?pageview_id=aec61d74-cf35-4cd3-52ae-8020a89f8cf3&amp;ad_position_id=676&amp;impression_group_id=plantuml_com-large-billboard-2/2025-05-31/4030693584591501&amp;ad_size=160x600&amp;domain_id=173770&amp;url=https://editor.plantuml.com/uml/"/></span></span></span>',true);
                    ezStaticAnchor('//*/span[@data-ez-ph-id="100"]', '<span class="ezoic-ad ezoic-at-4 medrectangle-2 medrectangle-2100 adtester-container adtester-container-100" data-ez-name="plantuml_com-medrectangle-2"><span id="div-gpt-ad-plantuml_com-medrectangle-2-0" ezaw="970" ezah="90" style="position:relative;z-index:0;display:inline-block;padding:0;" class="ezoic-ad"><script data-ezscrex="false" data-cfasync="false" type="text/javascript" style="display:none;">if(typeof ez_ad_units == "undefined"){ez_ad_units=[];}ez_ad_units.push([[970,90],"plantuml_com-medrectangle-2","ezslot_2",100,"0","0", "plantuml_com-medrectangle-2-0"]);if(typeof __ez_fad_position == "function"){__ez_fad_position("div-gpt-ad-plantuml_com-medrectangle-2-0");}</sc' + 'ript></span></span>',true);
                    __ez.queue.addFile('/detroitchicago/stickyfix.js', '/detroitchicago/stickyfix.js?gcb=2&cb=37', false, [], true, true, true, false);
                    __ez.queue.addFile('anchorfix.js', '/detroitchicago/anchorfix.js?cb=27', false, [], true, true, true, false);
                    var didTimeoutVign=false;setTimeout(function(){function getCookie(cname){var name=cname+"=";var ca=document.cookie.split(';');for(var i=0;i<ca.length;i++){var c=ca[i];while(c.charAt(0)==' '){c=c.substring(1);}
                    if(c.indexOf(name)==0){return c.substring(name.length,c.length);}}
                    return "";}
                    var cookieTest=getCookie("ezvignetteviewed");if(cookieTest==""){window.googletag=window.googletag||{};googletag.cmd=googletag.cmd||[];googletag.cmd.push(function(){if(typeof window.ezslot_interstitial!='undefined'&&window.ezslot_interstitial!=null){googletag.display(window.ezslot_interstitial);googletag.pubads().refresh([window.ezslot_interstitial]);if(typeof __ez!=='undefined'&&typeof __ez.pel!=='undefined'){__ez.pel.Add(window.ezslot_interstitial,[(new __ezDotData('fetched',1))]);}
                    didTimeoutVign=true;}});}},1000);if(typeof _ezaq!='undefined'&&typeof _ezaq.ab_test_id!='undefined'&&_ezaq.ab_test_id=='mod34'){document.addEventListener("click",expzscr);}
                    function expzscr(){if(!didTimeoutVign){return;}
                    var x=window.open();if(x){x.close();document.removeEventListener("click",expzscr);}}
                    __ez.queue.addFile('/detroitchicago/kenai.js', '/detroitchicago/kenai.js?gcb=2&cb=3f9035f7d9', false, [], true, false, true, false);
                    
                            function loadSovrnSignal() {
                                var sovrnSignalScript = document.createElement('script');
                                sovrnSignalScript.src = "//get.s-onetag.com/48e9aff7-e1fb-417c-a320-ed101cdab11f/tag.min.js";
                                sovrnSignalScript.async = true;
                                document.body.appendChild(sovrnSignalScript);
                            }
                            window.addEventListener('load', (e) => {
                                loadSovrnSignal();
                            });
                        
                    } catch(e) {window.ezstaticerrors = (window.ezstaticerrors || '') + e.toString();}window.ezFinishedStatic=true;</script><script type="text/javascript" src="https://link.rubiconproject.com/magnite/21150.js"></script><script async="" type="text/javascript" src="//securepubads.g.doubleclick.net/tag/js/gpt.js"></script><script src="//www.ezojs.com/detroitchicago/tuscon.js?gcb=2&amp;cb=14" async=""></script><script src="//www.ezojs.com/detroitchicago/kenai.js?gcb=2&amp;cb=3f9035f7d9" async=""></script><script src="//www.ezojs.com/detroitchicago/portland.js?gcb=2&amp;cb=a2ac4ddbb3" async=""></script><script src="/porpoiseant/banger.js?cb=195-2&amp;bv=447&amp;PageSpeed=off" async=""></script><script src="/detroitchicago/reportads.js?gcb=195-2&amp;cb=5"></script><style>.ezoic-ad.large-billboard-2676{align-items:center;box-sizing:content-box;display:flex !important;flex-direction:column !important;float:none !important;justify-content:center;line-height:0px;margin-left:0px !important;margin-right:0px !important;max-width:100% !important;min-height:600px;min-width:160px;padding:0;}</style><style>.ezoic-ad.medrectangle-2100{box-sizing:content-box;display:inline-block;float:none !important;line-height:0px;max-width:100% !important;min-height:90px;min-width:0px;padding:0;}</style><style>.ezoic-ad{display:inline-block;border:0px;}.ezoic-ad>div>iframe{margin:0px!important;padding:0px!important;width:revert-layer;}.ezoic-ad .ezoic-ad>div{text-align:center}</style><style>.ad-reporter-menu-backdrop{display:none;position:absolute;bottom:20px;right:15px;width:120px;height:100px;flex-direction:row;justify-content:center;align-items:center;background:transparent;box-shadow:#000 0 2px 10px;border-radius:10px;z-index:1002}.ad-reporter-menu{position:absolute;z-index:1003;display:flex;flex-direction:column;width:100%;height:100%;background:#fff;border-radius:5px;align-items:center;justify-content:space-around;font-weight:200;font-size:14px;font-family:sans-serif}.ad-reporter-menu .ad-reporter-menu-item{width:100%;height:100%;cursor:pointer;display:flex;justify-content:center;align-items:center}.ad-reporter-menu .ad-reporter-menu-item:not(:last-child){border-bottom:solid #d3d3d3 1px}</style><style>@keyframes ezIn { 			from { opacity: 0; } 		  }		  .ezoic-ad .ezoic-adl:before {content: '\00B7\00B7\00B7';position: absolute;display: flex!important;align-items: center;justify-content: center;text-align: center;color: #C4C4C4;font-size: 62px;letter-spacing: 2px;z-index: 0;animation: ezIn 1s infinite alternate;left: 50%;top: 50%;transform: translate(-50%, -50%);} .ezoic-ad .ezfound,.ezmob-footer .ezoic-ad .ezoic-ad,.ezoic-ad-adaptive > .ezoic-ad, .ezoic-ad-rl {background:0 0;border-radius:0;border:none}</style><style>.ezmob-footer{position:fixed;left:0;bottom:0;width:100%;background:#fff;z-index:100000;line-height:0}.ezmob-footer-desktop{background-color:#f6f6f6cc;white-space:nowrap}.ezmob-footer-close-wrap{height:90px;padding:3px 0 0 16px;display:inline-block;width:35px;vertical-align:top}span.ezmob-footer-close{cursor:pointer;display:inline-block;width:18px;height:18px;border-radius:50%;border:1px solid #696969;position:relative;background:#fff;opacity:1}span.ezmob-footer-close:hover{border:1px solid #5fa624}span.ezmob-footer-close::before,span.ezmob-footer-close::after{content:'';position:absolute;top:50%;left:50%;width:60%;height:1px;background:#696969}span.ezmob-footer-close::before{transform:translate(-50%,-50%) rotate(45deg)}span.ezmob-footer-close::after{transform:translate(-50%,-50%) rotate(-45deg)}img.ezmob-anchor-img{height:18px!important;padding:0!important;border:0!important;cursor:pointer!important;width:18px!important;margin:45px 0 3px 1px!important;box-sizing:content-box!important}@media(min-width:450px) and (max-width:780px){.ezmob-footer-close-wrap,.ezoicwhat{display:none!important}}.ezoicwhat{display:block}body {					padding-bottom: 100px !important;					height: auto;				}				</style><script src="//www.ezojs.com/detroitchicago/stickyfix.js?gcb=2&amp;cb=37" async=""></script><script src="//www.ezojs.com/detroitchicago/anchorfix.js?cb=27" async=""></script><script src="//www.ezojs.com/detroitchicago/kenai.js?gcb=2&amp;cb=3f9035f7d9" async=""></script><script src="https://carbon-cdn.ccgateway.net/script?id=editor.plantuml.com&amp;parentId=0dae949f4b"></script><meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><script src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202505290101/pubads_impl.js?cb=31092773" async=""></script><link href="https://securepubads.g.doubleclick.net/pagead/managed/dict/m202505290101/gpt" rel="compression-dictionary"><script src="https://editor.plantuml.com/porpoiseant/nmash.js?bv=447"></script><script src="https://config.aps.amazon-adsystem.com/configs/aa05931b-5308-4ea3-95a2-adf84f4ffde4" type="text/javascript" async="async"></script><script src="//secure.cdn.fastclick.net/js/pubcid/latest/pubcid.min.js"></script><script src="https://tags.crwdcntrl.net/lt/c/16576/sync.min.js"></script><script src="//cdn.id5-sync.com/api/1.0/id5-api.js"></script><script type="text/javascript" async="" src="https://secure.cdn.fastclick.net/js/cnvr-launcher/latest/launcher-stub.min.js"></script><script src="//go.ezodn.com/hb/dall.js?cb=195-2-122" async=""></script><style type="text/css">body > #ezmobfooter{bottom:0px;visibility:visible;}</style><script src="//go.ezodn.com/detroitchicago/audins.js?cb=3" async="" type="text/javascript"></script></head>
                    <body onload="brython()">
                    
                    
                    
                    <div id="external_container">
                    
                    <div id="header">
                        🎉 Discover PlantUML<br> Remastered by Samir-pro
                    </div>
                    
                    <div id="container">
                        <div id="cola">
                        </div>
                        <div id="editor" class=" ace_editor ace-github" style="font-size: 10pt;" draggable="false"><textarea class="ace_text-input" wrap="off" autocorrect="off" autocapitalize="off" spellcheck="false" style="opacity: 0; font-size: 1px; height: 1px; width: 1px; top: 15px; left: 44px;"></textarea><div class="ace_gutter" aria-hidden="true" style="left: 0px; width: 41px;"><div class="ace_layer ace_gutter-layer ace_folding-enabled" style="height: 1e+06px; top: 0px; left: 0px; width: 41px;"><div class="ace_gutter-cell ace_gutter-active-line " style="height: 15px; top: 0px;">1<span style="display: none;"></span></div></div></div><div class="ace_scroller" style="line-height: 15px; left: 40.4648px; right: 0px; bottom: 0px;"><div class="ace_content" style="top: 0px; left: 0px; width: 254.535px; height: 490px;"><div class="ace_layer ace_print-margin-layer"><div class="ace_print-margin" style="left: 601px; visibility: visible;"></div></div><div class="ace_layer ace_marker-layer"><div class="ace_active-line" style="height: 15px; top: 0px; left: 0px; right: 0px;"></div></div><div class="ace_layer ace_text-layer" style="height: 1e+06px; margin: 0px 4px; top: 0px; left: 0px;"><div class="ace_line" style="height: 15px; top: 0px;"></div></div><div class="ace_layer ace_marker-layer"></div><div class="ace_layer ace_cursor-layer ace_hidden-cursors"><div class="ace_cursor" style="display: block; top: 0px; left: 4px; width: 7px; height: 15px; animation-duration: 1000ms;"></div></div></div></div><div class="ace_scrollbar ace_scrollbar-v" style="width: 20px; bottom: 0px; display: none;"><div class="ace_scrollbar-inner" style="width: 20px; height: 15px;">&nbsp;</div></div><div class="ace_scrollbar ace_scrollbar-h" style="height: 20px; left: 40.4648px; right: 0px; display: none;"><div class="ace_scrollbar-inner" style="height: 20px; width: 239.605px;">&nbsp;</div></div><div style="height: auto; width: auto; top: 0px; left: 0px; visibility: hidden; position: absolute; white-space: pre; font: inherit; overflow: hidden;"><div style="height: auto; width: auto; top: 0px; left: 0px; visibility: hidden; position: absolute; white-space: pre; font: inherit; overflow: visible;">הההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההההה</div><div style="height: auto; width: auto; top: 0px; left: 0px; visibility: hidden; position: absolute; white-space: pre; font-style: inherit; font-variant: inherit; font-stretch: inherit; font-size: inherit; line-height: inherit; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; overflow: visible;">XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</div></div></div>
                        <div id="resizer"></div>
                        <div id="image-container">
                            <img id="diagram-image" style="display: block;">
                        </div>
                        <div id="colb">
                        </div>
                    </div>
                    
                    
                    <div id="footer">
                        <!-- Button to copy the text with an icon -->
                        <button class="btn" id="copy-btn">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                    
                        <!-- Button to paste the text with an icon -->
                        <button class="btn" id="paste-btn">
                            <i class="fas fa-paste"></i> Paste
                        </button>
                    
                        <!-- Button to save with an icon -->
                        <button class="btn" id="save-btn">
                            <i class="fas fa-save"></i> Save
                        </button>
                    
                        <!-- Button to load with an icon -->
                        <button class="btn" id="load-btn">
                            <i class="fas fa-folder-open"></i> List
                        </button>
                    
                        <!-- Toggle Button for Dark/Light Mode -->
                        <button class="btn" id="toggle-mode-btn">
                            <i class="fas fa-adjust"></i> Mode
                        </button>
                    
                        <!-- New Button to Update URL with an icon -->
                        <button class="btn" id="update-url-btn">
                            <i class="fas fa-link"></i> SyncURL
                        </button>
                        
                        <!-- New Button "Copy to MD" -->
                        <button class="btn" id="copy-md-btn">
                            <i class="fa-brands fa-markdown"></i> Copy
                        </button>
                    
                        <!-- New Button to Open a New Window with an icon -->
                        <button class="btn" id="open-window-btn">
                            <i class="fas fa-external-link"></i> Open Window
                        </button>
                        
                        <!-- Hyperlink to PNG image -->
                        <a href="https://img.plantuml.biz/plantuml/png/hLVDRjiu4Bu7o3iGxY5jjqxQgr4OTMm7CBgD3IVfDQ35iKoO8bMIIk0jze7sEVR5TYYA5YLBoHQxDv4pd9zlVlN5MAfjaGao5TIO6QUffjdvsVcPsNEPKpoGn54crcg-AImi4YNl-1z0FZgkd2PxcW8PpDShieOjQ909C3ANbbiEXhnT0sN3fHI7TmFo-VDKg8ADn-Jd-HdnWWc-_4QD1Vsur4p33Tredb3BbVHSXFn6GEVBxOAH4J5eXanHqkhhU1p8w09F12pO8pAY5KU-mC3099hdfT0sxKT1dR67bQ1IWjF1fGr4kmEb0QLJSUUGcb4BJWboCVoqF8CErhbalOmNP8cSccWGy8IQoPQ3O8PijSh8SZrRpnzl9jUEzqy7S0TAEUWIdkLD4w1mtuVJcm3ZOmmHijn4KAeWvPPcS8ALWH-52_1jaMtGXpwePeMN4G2eZe4peycEI-WBLgwCxIKYbe8Z2Xta5_62GdUWXRVzMILLTkIgF7bLsSHdOaFL96Jd2uegdYurfQQeqYPvBge26NwZ4k_YSaCgcLsNjOOfI3VKG2Wta4L67WpejQejNEP__-LTzEVf3hOJZx6_M8F9bJHq8s2b5IkIEf8Jbd79qTZZ-mOgaQ5EQur9WQSIZOU7nQo5XQF9qzGRaXqrEs0h5FoiD3l92-ry6XtTYpE-ZdHNufEhYsNP6AWWWiiziSh5a_XdIbzSHOwL4DvJiyTcO0iJN5g1P4TR5X9nIZKOKn4f-iGgT5MM2x3X-9qAxhe3Ey6kXjffgw6pUEg6xash1_JuMlPeKpbvsb6yjw_qYZunhVOTP67Ry6YD8kY26dIMfzXs7ygSgBHLpbMwxbKxbMh9Jnv1XnDsMDIJnHqN7zyMGhZShHrx2g1V7tv9wGVIb1Md18wr1ETbEdy2QUyF-R6yog4HvS6nfeem3-wiokkl7Cjv3RdoZnefqT1HPqGIN_UbXa-RFaURoLOZLVmAF5YYppj5C9MsIcVOau9XTR3WYCxe5B4cKqSmWrNc21CrKQAVs7VNu6j7p_TT2wPExdQoDD1lIglXRrlJtTLqIPqgTZfI-rQJPYiLeAEOKtVyFsDTYQnYqpGG8s7Su6dV9-XKN-457cucoLwgPm4iBLUhZSBcJEMn-YFgVop7qeb8tB_g1yqG7x2DPLCbjpojDfQepChhht3e6-AU0Jjw0PqJ_0AhNfH2pOxdXZnpYqiGyVFw_AnTJODF0p8Sai77oyltW_Ou6951w9kk1Lz5LRSv35uJrcmaAAlrkgKiFFxK_HZdigzPWq-Rr4fVfHd5j8geUksBer-nb-9B09zaz40GtJG5tUMgrzJnBauExsXNMAxglvWmtgBjwdULklts0wd_QaxClhmSzsphQ8IkXTTB1k_olYMc_MlK8KkpJaIwx77RS0bbRjhx3dchgdNe7GfxmO9cNlHwZ7LXAPUcnFiBBahui_aF" target="_blank" id="png-link" title="PNG Image" style="margin-left: 25px;"><i class="fas fa-image"></i></a>
                    
                        <!-- Hyperlink to SVG image -->
                        <a href="https://img.plantuml.biz/plantuml/svg/hLVDRjiu4Bu7o3iGxY5jjqxQgr4OTMm7CBgD3IVfDQ35iKoO8bMIIk0jze7sEVR5TYYA5YLBoHQxDv4pd9zlVlN5MAfjaGao5TIO6QUffjdvsVcPsNEPKpoGn54crcg-AImi4YNl-1z0FZgkd2PxcW8PpDShieOjQ909C3ANbbiEXhnT0sN3fHI7TmFo-VDKg8ADn-Jd-HdnWWc-_4QD1Vsur4p33Tredb3BbVHSXFn6GEVBxOAH4J5eXanHqkhhU1p8w09F12pO8pAY5KU-mC3099hdfT0sxKT1dR67bQ1IWjF1fGr4kmEb0QLJSUUGcb4BJWboCVoqF8CErhbalOmNP8cSccWGy8IQoPQ3O8PijSh8SZrRpnzl9jUEzqy7S0TAEUWIdkLD4w1mtuVJcm3ZOmmHijn4KAeWvPPcS8ALWH-52_1jaMtGXpwePeMN4G2eZe4peycEI-WBLgwCxIKYbe8Z2Xta5_62GdUWXRVzMILLTkIgF7bLsSHdOaFL96Jd2uegdYurfQQeqYPvBge26NwZ4k_YSaCgcLsNjOOfI3VKG2Wta4L67WpejQejNEP__-LTzEVf3hOJZx6_M8F9bJHq8s2b5IkIEf8Jbd79qTZZ-mOgaQ5EQur9WQSIZOU7nQo5XQF9qzGRaXqrEs0h5FoiD3l92-ry6XtTYpE-ZdHNufEhYsNP6AWWWiiziSh5a_XdIbzSHOwL4DvJiyTcO0iJN5g1P4TR5X9nIZKOKn4f-iGgT5MM2x3X-9qAxhe3Ey6kXjffgw6pUEg6xash1_JuMlPeKpbvsb6yjw_qYZunhVOTP67Ry6YD8kY26dIMfzXs7ygSgBHLpbMwxbKxbMh9Jnv1XnDsMDIJnHqN7zyMGhZShHrx2g1V7tv9wGVIb1Md18wr1ETbEdy2QUyF-R6yog4HvS6nfeem3-wiokkl7Cjv3RdoZnefqT1HPqGIN_UbXa-RFaURoLOZLVmAF5YYppj5C9MsIcVOau9XTR3WYCxe5B4cKqSmWrNc21CrKQAVs7VNu6j7p_TT2wPExdQoDD1lIglXRrlJtTLqIPqgTZfI-rQJPYiLeAEOKtVyFsDTYQnYqpGG8s7Su6dV9-XKN-457cucoLwgPm4iBLUhZSBcJEMn-YFgVop7qeb8tB_g1yqG7x2DPLCbjpojDfQepChhht3e6-AU0Jjw0PqJ_0AhNfH2pOxdXZnpYqiGyVFw_AnTJODF0p8Sai77oyltW_Ou6951w9kk1Lz5LRSv35uJrcmaAAlrkgKiFFxK_HZdigzPWq-Rr4fVfHd5j8geUksBer-nb-9B09zaz40GtJG5tUMgrzJnBauExsXNMAxglvWmtgBjwdULklts0wd_QaxClhmSzsphQ8IkXTTB1k_olYMc_MlK8KkpJaIwx77RS0bbRjhx3dchgdNe7GfxmO9cNlHwZ7LXAPUcnFiBBahui_aF" target="_blank" id="svg-link" title="SVG Image" style="margin-left: 15px;"><i class="fas fa-vector-square"></i></a>
                    
                        <!-- Hyperlink to ASCII Art image -->
                        <a href="https://img.plantuml.biz/plantuml/txt/hLVDRjiu4Bu7o3iGxY5jjqxQgr4OTMm7CBgD3IVfDQ35iKoO8bMIIk0jze7sEVR5TYYA5YLBoHQxDv4pd9zlVlN5MAfjaGao5TIO6QUffjdvsVcPsNEPKpoGn54crcg-AImi4YNl-1z0FZgkd2PxcW8PpDShieOjQ909C3ANbbiEXhnT0sN3fHI7TmFo-VDKg8ADn-Jd-HdnWWc-_4QD1Vsur4p33Tredb3BbVHSXFn6GEVBxOAH4J5eXanHqkhhU1p8w09F12pO8pAY5KU-mC3099hdfT0sxKT1dR67bQ1IWjF1fGr4kmEb0QLJSUUGcb4BJWboCVoqF8CErhbalOmNP8cSccWGy8IQoPQ3O8PijSh8SZrRpnzl9jUEzqy7S0TAEUWIdkLD4w1mtuVJcm3ZOmmHijn4KAeWvPPcS8ALWH-52_1jaMtGXpwePeMN4G2eZe4peycEI-WBLgwCxIKYbe8Z2Xta5_62GdUWXRVzMILLTkIgF7bLsSHdOaFL96Jd2uegdYurfQQeqYPvBge26NwZ4k_YSaCgcLsNjOOfI3VKG2Wta4L67WpejQejNEP__-LTzEVf3hOJZx6_M8F9bJHq8s2b5IkIEf8Jbd79qTZZ-mOgaQ5EQur9WQSIZOU7nQo5XQF9qzGRaXqrEs0h5FoiD3l92-ry6XtTYpE-ZdHNufEhYsNP6AWWWiiziSh5a_XdIbzSHOwL4DvJiyTcO0iJN5g1P4TR5X9nIZKOKn4f-iGgT5MM2x3X-9qAxhe3Ey6kXjffgw6pUEg6xash1_JuMlPeKpbvsb6yjw_qYZunhVOTP67Ry6YD8kY26dIMfzXs7ygSgBHLpbMwxbKxbMh9Jnv1XnDsMDIJnHqN7zyMGhZShHrx2g1V7tv9wGVIb1Md18wr1ETbEdy2QUyF-R6yog4HvS6nfeem3-wiokkl7Cjv3RdoZnefqT1HPqGIN_UbXa-RFaURoLOZLVmAF5YYppj5C9MsIcVOau9XTR3WYCxe5B4cKqSmWrNc21CrKQAVs7VNu6j7p_TT2wPExdQoDD1lIglXRrlJtTLqIPqgTZfI-rQJPYiLeAEOKtVyFsDTYQnYqpGG8s7Su6dV9-XKN-457cucoLwgPm4iBLUhZSBcJEMn-YFgVop7qeb8tB_g1yqG7x2DPLCbjpojDfQepChhht3e6-AU0Jjw0PqJ_0AhNfH2pOxdXZnpYqiGyVFw_AnTJODF0p8Sai77oyltW_Ou6951w9kk1Lz5LRSv35uJrcmaAAlrkgKiFFxK_HZdigzPWq-Rr4fVfHd5j8geUksBer-nb-9B09zaz40GtJG5tUMgrzJnBauExsXNMAxglvWmtgBjwdULklts0wd_QaxClhmSzsphQ8IkXTTB1k_olYMc_MlK8KkpJaIwx77RS0bbRjhx3dchgdNe7GfxmO9cNlHwZ7LXAPUcnFiBBahui_aF" target="_blank" id="ascii-link" title="ASCII Art" style="margin-left: 15px;"><i class="fas fa-font"></i></a>
                    
                        <div class="tooltip-container">
                            <input id="encoded-input" type="text" value="">
                    
                            <button class="decode-btn" id="decode-btn">
                                <i class="fas fa-unlock-alt"></i> Decode
                            </button>
                        </div>
                    </div>
                    
                    </div>
                    
                    <style>
                        .ace_style {
                            color: #111;
                        }
                        .dark-mode .ace_style {
                            color: #eee;
                        }
                    
                        .ace_keyword2 {
                            color: #333;
                            font-weight: bold;
                        }
                        .dark-mode .ace_keyword2 {
                            color: #E0E0E0;
                        }
                        .ace_preprocessor {
                            color: #22863a;
                            font-weight: bold;
                        }
                        .dark-mode .ace_preprocessor {
                            color: #79b8ff;
                            font-weight: bold;
                        }
                    
                        .ace_keyword {
                            color: #b31d28;
                        }
                    
                        .ace_identifier {
                            color: #0366d6;
                        }
                        .dark-mode .ace_identifier {
                            color: #539bf5;
                        }
                    
                    
                        .ace_bracket {
                            font-weight: bold;
                        }
                    
                    </style>
                    
                    
                    <script>
                        ace.define("ace/mode/plantuml", ["require", "exports", "ace/lib/oop", "ace/mode/text", "ace/mode/text_highlight_rules"], function(acequire, exports) {
                            var oop = acequire("ace/lib/oop");
                            var TextMode = acequire("ace/mode/text").Mode;
                            var TextHighlightRules = acequire("ace/mode/text_highlight_rules").TextHighlightRules;
                    
                            function PlantUMLHighlightRules() {
                                this.$rules = {
                                    "start": [
                                        { token: "style", regex: "^\\<style\\>", next: "style" },
                                        { token: "comment", regex: "^\\s*'.*" },
                                        { token: "comment", regex: "/'", next: "comment" },
                                        { token: "keyword2", regex: "@startuml|@enduml" },
                                        { token: "keyword", regex: "[-+<>=]+" },
                                        { token: "keyword", regex: "\\b(abstract|action|actor|agent|annotation|archimate|artifact|boundary|card|class|cloud|collections|component|control|database|diamond|entity|enum|exception|file|folder|frame|hexagon|interface|json|label|map|metaclass|node|object|package|participant|person|process|protocol|queue|rectangle|relationship|stack|state|storage|struct|usecase)\\b" },
                                        { token: "keyword", regex: "\\b(across|activate|again|allow_mixing|allowmixing|also|alt|as|attribute|attributes|autonumber|bold|bottom|box|break|caption|center|circle|circled|circles|color|create|critical|dashed|deactivate|description|destroy|detach|dotted|down|else|elseif|empty|end|endcaption|endfooter|endheader|endif|endlegend|endtitle|endwhile|false|field|fields|footbox|footer|fork|group|header|hide|hnote|if|is|italic|kill|left|left to right direction|legend|link|loop|mainframe|member|members|method|methods|namespace|newpage|normal|note|of|on|opt|order|over|package|page|par|partition|plain|private|protected|public|ref|repeat|return|right|rnote|rotate|show|skin|skinparam|split|sprite|start|stereotype|stereotypes|stop|style|then|title|together|top|top to bottom direction|true|up|while)\\b" },
                                        { token: "string", regex: '".*?"' },
                                        { token: "preprocessor", regex: "^![a-zA-Z_]+" },
                                        { token: "bracket", regex: "[{}\\[\\]]" },
                                        { token: "identifier", regex: "\\$[a-zA-Z_][a-zA-Z0-9_]*\\b" },
                                        { token: "constant.numeric", regex: "\\b[0-9]+" }
                                    ],
                                    "style": [
                                        { token: "style", regex: "^\\</style\\>", next: "start" },
                                        { token: "style", regex: ".+" }
                                    ],
                                    "comment": [
                                        { token: "comment", regex: ".*?'/", next: "start" },
                                        { token: "comment", regex: ".+" }
                                    ]
                                };
                                this.normalizeRules();
                    
                            }
                            oop.inherits(PlantUMLHighlightRules, TextHighlightRules);
                    
                            function PlantUMLMode() {
                                this.HighlightRules = PlantUMLHighlightRules;
                                this.blockComment = { start: "/'", end: "'/" };
                            }
                            oop.inherits(PlantUMLMode, TextMode);
                    
                            exports.Mode = PlantUMLMode;
                        });
                    
                        ace.edit("editor").session.setMode("ace/mode/plantuml");
                        if (window.localStorage.getItem("toggle_mode") === "dark") {
                            document.body.classList.toggle("dark-mode");
                            ace.edit("editor").setTheme("ace/theme/dracula");
                        } else {
                            ace.edit("editor").setTheme("ace/theme/github");
                        }
                    </script>
                    
                    
                    <script type="text/python">
                        from browser import document, window, html, timer
                        import re
                    
                        external_win = None
                    
                        def encode6bit(b):
                            if b < 10:
                                return chr(48 + b)
                            if b < 36:
                                return chr(65 + (b - 10))
                            if b < 62:
                                return chr(97 + (b - 36))
                            return '-' if b == 62 else '_'
                    
                        def append3bytes(b1, b2, b3):
                            c1 = b1 >> 2
                            c2 = ((b1 & 0x3) << 4) | (b2 >> 4)
                            c3 = ((b2 & 0xF) << 2) | (b3 >> 6)
                            c4 = b3 & 0x3F
                            return encode6bit(c1 & 0x3F) + encode6bit(c2 & 0x3F) + encode6bit(c3 & 0x3F) + encode6bit(c4 & 0x3F)
                    
                        def encode64(data):
                            r = ""
                            i = 0
                            while i < len(data):
                                if i + 2 == len(data):
                                    r += append3bytes(data[i], data[i + 1], 0)
                                elif i + 1 == len(data):
                                    r += append3bytes(data[i], 0, 0)
                                else:
                                    r += append3bytes(data[i], data[i + 1], data[i + 2])
                                i += 3
                            return r
                    
                    
                        def decode64(data):
                            r = ""
                            i = 0
                            while i < len(data):
                                b1 = decode6bit(data[i])
                                b2 = decode6bit(data[i + 1])
                                b3 = decode6bit(data[i + 2])
                                b4 = decode6bit(data[i + 3])
                    
                                c1 = (b1 << 2) | (b2 >> 4)
                                c2 = ((b2 & 0xF) << 4) | (b3 >> 2)
                                c3 = ((b3 & 0x3) << 6) | b4
                    
                                r += chr(c1)
                                if b3 != 64:
                                    r += chr(c2)
                                if b4 != 64:
                                    r += chr(c3)
                    
                                i += 4
                    
                            return r
                    
                        def decode6bit(c):
                            if '0' <= c <= '9':
                                return ord(c) - 48
                            if 'A' <= c <= 'Z':
                                return ord(c) - 65 + 10
                            if 'a' <= c <= 'z':
                                return ord(c) - 97 + 36
                            return 62 if c == '-' else 63 if c == '_' else 64  # Padding character
                    
                        def compress_and_encode(data):
                            byte_array = window.TextEncoder.new("utf-8").encode(data)
                            compressed = window.pako.deflateRaw(byte_array)
                            compressed_list = list(compressed)  # Direct conversion to list
                            return encode64(compressed_list)
                    
                    
                        def decompress_and_decode(data):
                            decoded_base64 = decode64(data)
                            # Convert the decoded string into a byte array
                            byteArray = window.Uint8Array.new([ord(c) for c in decoded_base64])
                            inflated = window.pako.inflateRaw(byteArray)
                            return window.TextDecoder.new("utf-8").decode(inflated)
                    
                    
                        # Function to retrieve URL parameters
                        def get_url_param(param_name):
                            url_params = window.location.search
                            params = dict(p.split('=') for p in url_params[1:].split('&') if '=' in p)
                            return params.get(param_name, None)
                    
                        match = re.search(r'[-_0-9A-Za-z]{20,}', window.location.href)
                        if match:
                            text_diagram_encoded = match.group(0)
                        else:
                            text_diagram_encoded = "SoWkIImgAStDuNBCoKnELT2rKt3AJrAmKiX8pSd9vt98pKi1IG80"
                    
                        plantuml_server = "https://img.plantuml.biz"
                    
                        def image_update(encoded):
                            global external_win
                            document["encoded-input"].value = encoded
                            is_dark_mode = 'dark-mode' in document.body.classList
                            suffix = 'd' if is_dark_mode else ''
                            img_url = f"{plantuml_server}/plantuml/{suffix}png/{encoded}"
                            document["png-link"].attrs['href'] = img_url
                            document["svg-link"].attrs['href'] = f"{plantuml_server}/plantuml/{suffix}svg/{encoded}"
                            document["ascii-link"].attrs['href'] = f"{plantuml_server}/plantuml/txt/{encoded}"
                    
                            def on_image_load(event):
                                width = img_element.width
                                height = img_element.height
                                print(f"{width=} {height=}")
                    
                            if external_win and not external_win.closed:
                                img_element = external_win.document.getElementById("external-image")
                                img_element.src = img_url
                                #print(external_win.document["external-image"])
                                #img_element = external_win.document["external-image"]
                            else:
                                img_element = document["diagram-image"]
                                img_element.bind("load", on_image_load)
                                img_element.attrs['src'] = img_url
                                img_element.style['display'] = 'block'  # set visible
                    
                    
                    
                        # Display or use the retrieved value
                        text_diagram = decompress_and_decode(text_diagram_encoded)
                    
                        image_update(text_diagram_encoded)
                    
                        # Using custom mode in the ACE editor
                        editor = window.ace.edit("editor")
                    
                        # Set the initial content
                        editor.setValue(text_diagram, 1)   # The second argument places the cursor at the end of the content
                    
                        # Optional settings
                        editor.setOptions({
                            "fontSize": "10pt",
                            "showLineNumbers": True,
                            "tabSize": 4
                        })
                    
                        # Variable to store the timer ID
                        timeout_id = None
                    
                        def editor_change(*args):
                            global timeout_id
                            # Retrieve the value from the editor
                            v = editor.getValue()
                            if len(v)>0:
                                encoded = compress_and_encode(v)
                                print(f"editor_change {len(v)=} {encoded=}")
                                document["encoded-input"].value = encoded
                                if timeout_id:
                                    timer.clear_timeout(timeout_id)
                    
                                timeout_id = timer.set_timeout(lambda: image_update(encoded), 1500)
                    
                    
                        # Bind the change event to the editor_change function
                        editor.session.on('change', editor_change)
                    
                        # Resize logic
                        resizer = document["resizer"]
                        editorDiv = document["editor"]
                        imageContainer = document["image-container"]
                    
                        def resize(event):
                            containerRect = document["container"].getBoundingClientRect()
                            colaRect = document["cola"].getBoundingClientRect()
                    
                            newWidth = event.clientX - containerRect.left - colaRect.width
                    
                            editorDiv.style.width = f'{newWidth}px'
                    
                        def stopResize(event):
                            document.unbind('mousemove', resize)
                            document.unbind('mouseup', stopResize)
                    
                        def startResize(event):
                            event.preventDefault()
                            document.bind('mousemove', resize)
                            document.bind('mouseup', stopResize)
                    
                        resizer.bind('mousedown', startResize)
                    
                    
                        # Function that will be called when the encoded value changes
                        def click_on_decode(event):
                            input_value = document["encoded-input"].value
                            # Check if a slash is present and retrieve the text after the last slash
                            if '/' in input_value:
                                input_value = input_value.rpartition('/')[-1]  # Take the part after the last slash
                    
                            print(f"on_encoded_input_change {input_value=}")
                            uml = decompress_and_decode(input_value)
                            print(f"on_encoded_input_change {uml=}")
                            editor.setValue(uml)
                    
                    
                        # Bind the function to the 'click' event of the "Decode" button
                        document["decode-btn"].bind("click", click_on_decode)
                    
                        # Function to copy the diagram text to the clipboard
                        def copy_to_clipboard(event):
                            editor_value = window.ace.edit("editor").getValue()  # Get the text from the editor
                            window.navigator.clipboard.writeText(editor_value)  # Copy to the clipboard
                    
                        # Bind the copy_to_clipboard function to the "Copy" button
                        document["copy-btn"].bind("click", copy_to_clipboard)
                    
                        # Function to toggle dark/light mode
                        def toggle_mode(event):
                            document.body.classList.toggle("dark-mode")
                            # Update ACE editor theme based on the mode
                            if 'dark-mode' in document.body.classList:
                                editor.setTheme("ace/theme/dracula")
                                window.localStorage.setItem("toggle_mode", "dark")
                            else:
                                editor.setTheme("ace/theme/github")
                                window.localStorage.setItem("toggle_mode", "light")
                    
                            # Re-update the diagram image source based on the encoded value
                            current_encoded = document["encoded-input"].value
                            image_update(current_encoded)
                    
                        # Bind the function to the 'click' event of the toggle button
                        document["toggle-mode-btn"].bind("click", toggle_mode)
                    
                        # Function to sync url
                        def update_url(event):
                            url = window.location.href
                            print(f"{url=}")
                            base_url_parts = url.split("/", 3)
                            base_url = "/".join(base_url_parts[:3])
                            if window.location.href.startswith("https://"):
                                window.location.href = f"https://editor.plantuml.com/uml/" + document["encoded-input"].value
                            else:
                                window.location.href = f"{base_url}/?uml=" + document["encoded-input"].value
                    
                        # Bind the function to the 'click' event of the toggle button
                        document["update-url-btn"].bind("click", update_url)
                    
                        # Function to copy to Markdown format
                        def copy_to_markdown(event):
                            encoded = document["encoded-input"].value
                            is_dark_mode = 'dark-mode' in document.body.classList
                            suffix = 'd' if is_dark_mode else ''
                            img_url = f"{plantuml_server}/plantuml/{suffix}svg/{encoded}"
                            link_url = f"https://editor.plantuml.com/uml/" + encoded
                            markdown_text = f"[![]({img_url})]({link_url})"
                            window.navigator.clipboard.writeText(markdown_text)  # Copy to the clipboard
                    
                        document["copy-md-btn"].bind("click", copy_to_markdown)
                    
                    
                    
                        def open_window(event):
                            global external_win
                            if external_win and not external_win.closed:
                                img_url = external_win.document.getElementById("external-image").src
                                external_win.close()
                                external_win = None
                                document["image-container"].style['display'] = 'block'
                                document["editor"].style.width = f'{round(window.innerWidth/2-160)}px'
                                document["diagram-image"].attrs['src'] = img_url
                                return
                    
                            w = round(window.innerWidth * .75)
                            h = round(window.innerHeight * .75)
                            options = f"width={w},height={h},toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,copyhistory=no"
                    
                            document["image-container"].style['display'] = 'none'
                            document["editor"].style.width = f'{window.innerWidth - 20}px'
                    
                            img_url = document["diagram-image"].attrs['src']
                    
                            external_win = window.open('', '_blank', options)
                    
                            html_content = f'''
                            <html>
                            <body>
                            <img id="external-image" src="{img_url}">
                            </body>
                            </html>
                            '''
                    
                            external_win.document.write(html_content)
                            external_win.document.close()
                    
                        document["open-window-btn"].bind("click", open_window)
                    
                        # Function to paste the clipboard text into the editor
                        def paste_from_clipboard(event):
                            # Use the clipboard API to read the text
                            window.navigator.clipboard.readText().then(paste_text)
                    
                        # Function that pastes the retrieved text into the editor
                        def paste_text(text):
                            editor = window.ace.edit("editor")  # Retrieve the ACE editor
                            editor.setValue(text, 1)  # Overwrite all content with the clipboard text, and place the cursor at the end
                    
                        # Bind the paste_from_clipboard function to the "Paste" button
                        document["paste-btn"].bind("click", paste_from_clipboard)
                    
                        def save_file(event):
                            # Generate an encoded date in the format YYYY_MM_DD__HH_MM_SS with JavaScript
                            current_time = window.Date.new().toISOString().replace(":", "_").replace("-", "_").split(".")[0].replace("T", "__")
                            
                            # Propose the encoded date as the default file name
                            filename = window.prompt("This will be saved locally in your browser:", f"diagram_{current_time}")
                            
                            if filename:
                                # Retrieve the content from the editor
                                editor_value = window.ace.edit("editor").getValue()
                                print(editor_value)
                                # Save in LocalStorage with the key provided by the user
                                window.localStorage.setItem("pl_" + filename, editor_value)
                                
                                # Confirmation of the save
                                print(f"Content saved under the key : {filename}")
                    
                        # Bind the save_file function to the "Save" button
                        document["save-btn"].bind("click", save_file)
                    
                    
                        def load_file(event):
                            # Retrieve all keys that start with "pl_"
                            keys = [window.localStorage.key(i) for i in range(window.localStorage.length) if window.localStorage.key(i).startswith("pl_")]
                            
                            if not keys:
                                window.alert("Nothing to reload.")
                                return
                    
                            # Sort the keys alphabetically, including the "pl_" prefix
                            keys.sort()  # Sort directly, including the 'pl_' prefix
                    
                            # Create a background div that covers the entire page
                            back_div = document.createElement('div')
                            back_div.id = "back-div"
                            
                            # Function to close both divs when clicking on back_div
                            def close_on_click(event):
                                back_div.remove()
                                load_div.remove()
                    
                            # Bind click event to back_div to close it when clicked
                            back_div.bind("click", close_on_click)
                                    
                            # Add the back_div to the document
                            document.body <= back_div
                        
                            # Create a dynamic div to display the key options
                            load_div = document.createElement('div')
                            load_div.id = "load-div"
                            
                            # Insert table instead of buttons
                            load_div.innerHTML = "<strong>Available Files</strong><br><br>"
                            
                            table = html.TABLE()
                            table_body = html.TBODY()
                    
                            # Add rows for each file
                            for i, key in enumerate(keys):
                                row = html.TR()
                    
                                # Delete button column
                                delete_button = html.BUTTON(Class="delete-btn")
                                delete_button <= html.I(Class="fas fa-trash")  # Add Font Awesome trash icon inside the button
                                delete_button.bind("click", lambda ev, k=key: delete_file(k))
                                delete_col = html.TD(delete_button)
                                delete_col.style.width = "1%"  # Set a small width for the delete column
                                
                                # File name column
                                file_col = html.TD()
                                file_name_span = html.SPAN(key[3:])
                                file_col.bind("click", lambda ev, k=key: load_key(k))  # Bind click event to the span
                                file_col <= file_name_span  # Add span to the file_col
                    
                                # Append columns to row
                                row <= delete_col
                                row <= file_col
                    
                                # Append row to the table body
                                table_body <= row
                    
                            # Append table body to the table
                            table <= table_body
                    
                            # Add table to load_div
                            load_div <= table
                            
                            # Add the div to the document
                            document.body <= load_div
                    
                        def delete_file(chosen_key):
                            # Delete the file from LocalStorage
                            window.localStorage.removeItem(chosen_key)
                            
                            # Reload the div to reflect the changes
                            document["load-div"].remove()
                            load_file(None)
                    
                    
                        def load_key(chosen_key):
                            # Load the value associated with the chosen key
                            editor_value = window.localStorage.getItem(chosen_key)
                            
                            # Reload the value into the editor
                            window.ace.edit("editor").setValue(editor_value)
                            print(f"Content loaded from the key : {chosen_key}")
                            
                            # Remove the div after loading the data
                            document["load-div"].remove()
                            document["back-div"].remove()
                    
                        # Bind the load_file function to the "Load" button
                        document["load-btn"].bind("click", load_file)
                    
                    
                        def check_external_win():
                            global external_win
                            if external_win and external_win.closed and document["image-container"].style['display'] == 'none':
                                img_url = external_win.document.getElementById("external-image").src
                                external_win = None
                                document["image-container"].style['display'] = 'block'
                                document["editor"].style.width = f'{round(window.innerWidth/2-160)}px'
                                document["diagram-image"].attrs['src'] = img_url
                    
                        # Only solution to restore image when closing the external window
                        timer.set_interval(check_external_win, 1000)
                    
                    </script>
                    
                    <!-- Default Statcounter code for Editor.plantuml.com
                    https://editor.plantuml.com -->
                    <script type="text/javascript">
                    var sc_project=13062078; 
                    var sc_invisible=1; 
                    var sc_security="d4f45758"; 
                    </script>
                    <script type="text/javascript" src="https://www.statcounter.com/counter/counter.js" async=""></script>
                    <noscript><div class="statcounter"><a title="site stats"
                    href="https://statcounter.com/" target="_blank"><img
                    class="statcounter"
                    src="https://c.statcounter.com/13062078/0/d4f45758/1/"
                    alt="site stats"
                    referrerPolicy="no-referrer-when-downgrade"></a></div></noscript>
                    <!-- End of Statcounter Code -->
                    
                    
                    
                    
                    <!--[selectrongo:done]--><span id="ezoic-pub-ad-placeholder-100"></span><span data-ez-ph-id="100" style="line-height:0px;padding:0;float:none !important;box-sizing:content-box;display:inline-block;min-width:0px;min-height:90px;max-width:100% !important;"><span class="ezoic-ad ezoic-at-4 medrectangle-2 medrectangle-2100 adtester-container adtester-container-100" data-ez-name="plantuml_com-medrectangle-2"><span id="div-gpt-ad-plantuml_com-medrectangle-2-0" ezaw="970" ezah="90" style="position:relative;z-index:0;display:inline-block;padding:0;" class="ezoic-ad"><script data-ezscrex="false" data-cfasync="false" type="text/javascript" style="display:none;">if(typeof ez_ad_units == "undefined"){ez_ad_units=[];}ez_ad_units.push([[970,90],"plantuml_com-medrectangle-2","ezslot_2",100,"0","0", "plantuml_com-medrectangle-2-0"]);if(typeof __ez_fad_position == "function"){__ez_fad_position("div-gpt-ad-plantuml_com-medrectangle-2-0");}</script></span></span></span><script>!function(){function t(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}!function(){function e(){return document.querySelector('ins[data-anchor-shown="true"][data-anchor-status="displayed"]')}function r(t){var e=t.querySelector("iframe");e&&requestAnimationFrame((function(){var r=parseFloat(getComputedStyle(e).height);isNaN(r)||t.style.setProperty("height","".concat(r+5,"px"),"important")}))}function n(t){t.style.setProperty("width","100%","important"),t.style.maxWidth="none"}function o(t){var e=t.querySelector("iframe");e&&(e.style.setProperty("display","block","important"),e.style.setProperty("margin","0 auto","important"))}function a(e){if(e){var a=new WeakSet;a.has(e)||(a.add(e),new MutationObserver((function(a){var i,u=function(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return t(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(e))||r&&e&&"number"==typeof e.length){n&&(e=n);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,u=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var t=n.next();return u=t.done,t},e:function(t){c=!0,i=t},f:function(){try{u||null==n.return||n.return()}finally{if(c)throw i}}}}(a);try{for(u.s();!(i=u.n()).done;)"style"===i.value.attributeName&&(r(e),n(e),o(e))}catch(t){u.e(t)}finally{u.f()}})).observe(e,{attributes:!0,attributeFilter:["style"]}))}}!function(){var t=e();if(t)return r(t),n(t),o(t),void a(t);var i=new MutationObserver((function(){var t=e();t&&(r(t),n(t),o(t),a(t),i.disconnect())}));i.observe(document.body,{attributes:!0,subtree:!0,childList:!0})}()}()}();</script>
                    <script type="text/javascript" data-cfasync="false">!function(){var e=function(e,t){for(var r=0;r<t.length;r++){var n=t[r];if(0==n.complete||void 0!==n.readyState&&n.readyState<4){var o=n.getAttribute("src")||n.currentSrc;void 0!==n.readyState&&0==n.readyState?n.addEventListener("loadstart",(function(e){var t=e.currentTarget.getAttribute("src")||e.currentSrc;window.ezorqs(e,t)})):(o=n.getAttribute("src")||n.currentSrc,window.ezorqs(n,o)),n.addEventListener("load",(function(e){var t=e.currentTarget.getAttribute("src")||e.srcElement.currentSrc;window.ezorqe(e,t)})),n.addEventListener("loadeddata",(function(e){var t=e.currentTarget.getAttribute("src")||e.srcElement.currentSrc;window.ezorqe(e,t)})),n.addEventListener("error",(function(e){var t=e.currentTarget.getAttribute("src")||e.srcElement.currentSrc;window.ezorqe(e,t)}))}}};function t(e){for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].href==e)return!0;return!1}__ez_addAllListeners=function(){e(0,document.querySelectorAll("img")),e(0,document.querySelectorAll("video")),e(0,document.querySelectorAll("audio")),function(e){for(var r=0;r<e.length;r++){var n=e[r];if(("preload"==n.getAttribute("rel")||"stylesheet"==n.getAttribute("rel"))&&null!=n.getAttribute("href")&&t(n.getAttribute("href"))){window.ezorqs(n,n.getAttribute("href"));var o=document.createElement("img");o.onerror=function(e){void 0!==e.path&&void 0!==e.path[0].currentSrc?window.ezorqe(n,e.path[0].currentSrc):void 0!==e.srcElement&&void 0!==e.srcElement.href&&window.ezorqe(n,e.srcElement.href)},o.src=n.getAttribute("href")}}}(document.querySelectorAll("link")),void 0!==window.__ez.ssaf&&window.__ez.ssaf.indexOf(16)>-1&&void 0!==window.__ez.sshsdef&&!1===window.__ez.sshsdef&&Element.prototype.addEventListener&&("function"==typeof window.onload&&(window.addEventListener("load",window.onload),window.onload=null),"function"==typeof document.onload&&(document.addEventListener.addEventListener("load",document.onload),document.onload=null))},__ez.queue.addFunc("__ez_addAllListeners","__ez_addAllListeners",null,!1,["/detroitchicago/tulsa.js"],!0,!0,!0,!0)}();</script>
                    <script data-ezscrex="false" data-cfasync="false">
                            window.humixPlayers = window.humixPlayers || [];
                            window.humixPlayers.push({ target: 'autoinsert', isGenerated: true });
                        </script>
                    <script type="text/javascript" style="display:none;" async="">if (typeof window.__ez !== 'undefined' && window.__ez?.queue?.addFileOnce) {window.__ez.queue.addFileOnce('identity', 'https://go.ezodn.com/detroitchicago/indy.js?cb=37&gcb=0', true, [], true, false, false, true);} </script><script data-cfasync="false">function _emitEzConsentEvent(){var customEvent=new CustomEvent("ezConsentEvent",{detail:{ezTcfConsent:window.ezTcfConsent},bubbles:true,cancelable:true,});document.dispatchEvent(customEvent);}
                    (function(window,document){function _setAllEzConsentTrue(){window.ezTcfConsent.loaded=true;window.ezTcfConsent.store_info=true;window.ezTcfConsent.develop_and_improve_services=true;window.ezTcfConsent.measure_ad_performance=true;window.ezTcfConsent.measure_content_performance=true;window.ezTcfConsent.select_basic_ads=true;window.ezTcfConsent.create_ad_profile=true;window.ezTcfConsent.select_personalized_ads=true;window.ezTcfConsent.create_content_profile=true;window.ezTcfConsent.select_personalized_content=true;window.ezTcfConsent.understand_audiences=true;window.ezTcfConsent.use_limited_data_to_select_content=true;window.ezTcfConsent.select_personalized_content=true;}
                    function _clearEzConsentCookie(){document.cookie="ezCMPCookieConsent=tcf2;Domain=.plantuml.com;Path=/;expires=Thu, 01 Jan 1970 00:00:00 GMT";}
                    _clearEzConsentCookie();if(typeof window.__tcfapi!=="undefined"){window.ezgconsent=false;var amazonHasRun=false;function _ezAllowed(tcdata,purpose){return(tcdata.purpose.consents[purpose]||tcdata.purpose.legitimateInterests[purpose]);}
                    function _handleConsentDecision(tcdata){window.ezTcfConsent.loaded=true;if(!tcdata.vendor.consents["347"]&&!tcdata.vendor.legitimateInterests["347"]){window._emitEzConsentEvent();return;}
                    window.ezTcfConsent.store_info=_ezAllowed(tcdata,"1");window.ezTcfConsent.develop_and_improve_services=_ezAllowed(tcdata,"10");window.ezTcfConsent.measure_content_performance=_ezAllowed(tcdata,"8");window.ezTcfConsent.select_basic_ads=_ezAllowed(tcdata,"2");window.ezTcfConsent.create_ad_profile=_ezAllowed(tcdata,"3");window.ezTcfConsent.select_personalized_ads=_ezAllowed(tcdata,"4");window.ezTcfConsent.create_content_profile=_ezAllowed(tcdata,"5");window.ezTcfConsent.measure_ad_performance=_ezAllowed(tcdata,"7");window.ezTcfConsent.use_limited_data_to_select_content=_ezAllowed(tcdata,"11");window.ezTcfConsent.select_personalized_content=_ezAllowed(tcdata,"6");window.ezTcfConsent.understand_audiences=_ezAllowed(tcdata,"9");window._emitEzConsentEvent();}
                    function _handleGoogleConsentV2(tcdata){if(!tcdata||!tcdata.purpose||!tcdata.purpose.consents){return;}
                    var googConsentV2={};if(tcdata.purpose.consents[1]){googConsentV2.ad_storage='granted';googConsentV2.analytics_storage='granted';}
                    if(tcdata.purpose.consents[3]&&tcdata.purpose.consents[4]){googConsentV2.ad_personalization='granted';}
                    if(tcdata.purpose.consents[1]&&tcdata.purpose.consents[7]){googConsentV2.ad_user_data='granted';}
                    if(googConsentV2.analytics_storage=='denied'){gtag('set','url_passthrough',true);}
                    gtag('consent','update',googConsentV2);}
                    __tcfapi("addEventListener",2,function(tcdata,success){if(!success||!tcdata){window._emitEzConsentEvent();return;}
                    if(!tcdata.gdprApplies){_setAllEzConsentTrue();window._emitEzConsentEvent();return;}
                    if(tcdata.eventStatus==="useractioncomplete"||tcdata.eventStatus==="tcloaded"){if(typeof gtag!='undefined'){_handleGoogleConsentV2(tcdata);}
                    _handleConsentDecision(tcdata);if(tcdata.purpose.consents["1"]===true&&tcdata.vendor.consents["755"]!==false){window.ezgconsent=true;(adsbygoogle=window.adsbygoogle||[]).pauseAdRequests=0;}
                    if(window.__ezconsent){__ezconsent.setEzoicConsentSettings(ezConsentCategories);}
                    __tcfapi("removeEventListener",2,function(success){return null;},tcdata.listenerId);if(!(tcdata.purpose.consents["1"]===true&&_ezAllowed(tcdata,"2")&&_ezAllowed(tcdata,"3")&&_ezAllowed(tcdata,"4"))){if(typeof __ez=="object"&&typeof __ez.bit=="object"&&typeof window["_ezaq"]=="object"&&typeof window["_ezaq"]["page_view_id"]=="string"){__ez.bit.Add(window["_ezaq"]["page_view_id"],[new __ezDotData("non_personalized_ads",true),]);}}}});}else{_setAllEzConsentTrue();window._emitEzConsentEvent();}})(window,document);</script><script src="https://cdn.id5-sync.com/api/1.0/id5-api.js" async=""></script><script data-ezscrex="false" data-cfasync="false" async="false" src="https://www.humix.com/video.js"></script><script src="/edmontonalberta/calgary.js?cb=3a909d4c24" data-ezscrex="false" async="true" data-pagespeed-no-defer="true" type="text/javascript"></script><script src="https://go.ezodn.com/porpoiseant/ezadcreator.js?gcb=195-2&amp;cb=648" async=""></script><script src="/detroitchicago/augusta.js?cb=49" async="true" data-ezscrex="false"></script><script src="https://go.ezodn.com/porpoiseant/ezadloadrewarded.js?gcb=195-2&amp;cb=648" async=""></script><div id="uglipop_overlay_wrapper" style="position:absolute;top:0;bottom:0;left:0;right:0;display:none"><div id="uglipop_overlay" style="position: fixed; inset: 0px; opacity: 0.3; width: 100%; height: 100%; background-color: black; display: none;"></div></div><div id="uglipop_content_fixed" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); opacity: 1; z-index: 10000000; display: none;"><div id="uglipop_popbox"></div></div><script src="https://go.ezodn.com/porpoiseant/ezadloadhb.js?gcb=195-2&amp;cb=648" async=""></script><script src="https://go.ezodn.com/porpoiseant/ezadloadamzn.js?gcb=195-2&amp;cb=648" async=""></script><iframe marginwidth="0" marginheight="0" scrolling="no" frameborder="0" id="1c44dc0d2277c38" width="0" height="0" src="about:blank" name="__pb_locator__" style="display: none; height: 0px; width: 0px; border: 0px;"></iframe><iframe src="https://aax-eu.amazon-adsystem.com/s/iu3?cm3ppd=1&amp;d=dtb-pub&amp;csif=t&amp;dl=n-sharethrough_n-onetag_n-nativo" style="display: none;"></iframe><script src="https://script-api.ccgateway.net/1/userId" type="text/javascript" async=""></script><script src="https://script-api.ccgateway.net/script/launcher/2/user.js" type="text/javascript" async=""></script><script src="https://script-api.ccgateway.net/script/launcher/1/customevents.js" type="text/javascript" async=""></script><script src="https://script-api.ccgateway.net/script/launcher/5/api.js" type="text/javascript" async=""></script><script src="https://script-api.ccgateway.net/setUser?parent=0dae949f4b&amp;site=editor.plantuml.com&amp;ccuid=a966c96f-c468-41f5-ae7c-e3f176ae2aa5&amp;ccsid=062da4bc-e39c-46cb-867d-974bee1b14d5" type="text/javascript" async=""></script><script src="https://script-api.ccgateway.net/script/bundle?id=editor.plantuml.com&amp;parentId=0dae949f4b" type="text/javascript" async=""></script><script src="https://go.ezodn.com/porpoiseant/ezadfilled.js?gcb=195-2&amp;cb=648" async=""></script><div class="textads banner-ads banner_ads ad-unit ad-zone ad-space adsbox" style="height: 1px;"></div><script src="//get.s-onetag.com/48e9aff7-e1fb-417c-a320-ed101cdab11f/tag.min.js" async=""></script><iframe src="https://www.google.com/recaptcha/api2/aframe" width="0" height="0" style="display: none;"></iframe><script src="https://go.ezodn.com/porpoiseant/ezidentity.js?gcb=195-2&amp;cb=648" async=""></script><script src="https://go.ezodn.com/porpoiseant/ezjitscroll.js?gcb=195-2&amp;cb=648" async=""></script><script src="https://go.ezodn.com/porpoiseant/ezicsticky.js?gcb=195-2&amp;cb=648" async=""></script><script src="https://go.ezodn.com/porpoiseant/ezjitpos.js?gcb=195-2&amp;cb=648" async=""></script></body><iframe sandbox="allow-scripts allow-same-origin" id="14800a3d202d0d3" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://gum.criteo.com/syncframe?origin=criteoPrebidAdapter&amp;topUrl=editor.plantuml.com&amp;gpp=#{%22bundle%22:%22mCLU5l91THdZNUV0ZGtuQ1hWYmx2dkk3TDNkNFElMkJBc1NaMTNLMjcxNE1DNnJqbjJPOFN0U2szcnplcHhCclA3eHJJdGt4RXdsNVowOE5UaTM3Y21layUyQm42S1hrMmFvRERENVhpTzY4U2dmdExIZGMxVmxqWllHOTlwNVJuazB3NmlnakZmUjVpcW02aWhqMUw0VlNnamRmJTJGWDB3SkVtd05LVmZmaG02TjMyd1ByRnMlM0Q%22,%22cw%22:true,%22lsw%22:true,%22origin%22:%22criteoPrebidAdapter%22,%22requestId%22:%220.6051541919477459%22,%22tld%22:%22editor.plantuml.com%22,%22topUrl%22:%22editor.plantuml.com%22,%22version%22:%229_37_0%22}">
                        </iframe></html>
            </div>
        </div>
    </div>
    
    <div class="status" id="status">Content replaced successfully!</div>

    <script>
        const iframe = document.getElementById('plantumlFrame');
        const loading = document.getElementById('loading');
        const replacedContent = document.getElementById('replacedContent');
        const status = document.getElementById('status');
        
        // Function to replace content
        function replaceContent() {
            try {
                // Hide loading and iframe
                loading.style.display = 'none';
                iframe.style.display = 'none';
                
                // Show replaced content
                replacedContent.style.display = 'flex';
                
                // Show status message
                status.style.display = 'block';
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
                
                console.log('Content replaced successfully');
            } catch (error) {
                console.error('Error replacing content:', error);
            }
        }
        
        // Wait for iframe to load
        iframe.addEventListener('load', function() {
            console.log('PlantUML Editor loaded');
            
            // Show the iframe briefly
            loading.style.display = 'none';
            iframe.style.display = 'block';
            
            // Wait a bit more to ensure everything is fully loaded
            setTimeout(() => {
                replaceContent();
            }, 2000); // 2 second delay to ensure full loading
        });
        
        // Fallback timeout in case load event doesn't fire
        setTimeout(() => {
            if (loading.style.display !== 'none') {
                console.log('Fallback: Replacing content after timeout');
                replaceContent();
            }
        }, 10000); // 10 second fallback
        
        // Handle iframe load errors
        iframe.addEventListener('error', function() {
            console.error('Error loading PlantUML Editor');
            loading.innerHTML = '<p style="color: red;">Error loading PlantUML Editor</p>';
            setTimeout(() => {
                replaceContent();
            }, 2000);
        });
    </script>
</body>
</html>

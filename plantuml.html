<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PlantUML Editor - Standalone</title>
    <link
      rel="icon"
      href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌱</text></svg>"
    />

    <style>
      /* Modern CSS Reset & Variables */
      *,
      *::before,
      *::after {
        box-sizing: border-box;
      }
      * {
        margin: 0;
      }
      body {
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
      }
      img,
      picture,
      video,
      canvas,
      svg {
        display: block;
        max-width: 100%;
      }
      input,
      button,
      textarea,
      select {
        font: inherit;
      }
      p,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        overflow-wrap: break-word;
      }

      :root {
        --primary-color: #2563eb;
        --primary-hover: #1d4ed8;
        --bg-color: #ffffff;
        --text-color: #1f2937;
        --border-color: #e5e7eb;
        --editor-bg: #ffffff;
        --editor-gutter: #f9fafb;
        --editor-text: #374151;
        --button-bg: #f3f4f6;
        --button-hover: #e5e7eb;
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
      }

      .dark-mode {
        --bg-color: #111827;
        --text-color: #f9fafb;
        --border-color: #374151;
        --editor-bg: #1f2937;
        --editor-gutter: #374151;
        --editor-text: #f9fafb;
        --button-bg: #374151;
        --button-hover: #4b5563;
      }

      /* Layout */
      body {
        background: var(--bg-color);
        color: var(--text-color);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        margin: 0;
        padding: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      #header {
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--primary-hover)
        );
        color: white;
        padding: 12px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        font-size: 14px;
        box-shadow: var(--shadow);
        position: relative;
        z-index: 10;
      }
      #header span {
        font-weight: 600;
      }

      #external_container {
        flex: 1;
        display: flex;
        flex-direction: column;
        max-width: 100vw;
        overflow: hidden;
      }
      #container {
        flex: 1;
        display: flex;
        gap: 1px;
        background: var(--border-color);
        min-height: 0;
      }

      #editor-panel {
        flex: 1;
        min-width: 300px;
        background: var(--editor-bg);
        display: flex;
        flex-direction: column;
      }
      #editor {
        flex: 1;
        font-family: "SF Mono", Monaco, Inconsolata, "Liberation Mono", Consolas,
          monospace;
        font-size: 14px;
        line-height: 1.4;
        padding: 16px;
        background: var(--editor-bg);
        color: var(--editor-text);
        border: none;
        outline: none;
        resize: none;
        overflow: auto;
        white-space: pre;
        word-wrap: break-word;
      }

      #resizer {
        width: 6px;
        background: var(--border-color);
        cursor: col-resize;
        position: relative;
        transition: background-color 0.2s;
      }
      #resizer:hover {
        background: var(--primary-color);
      }
      #resizer:active {
        background: var(--primary-hover);
      }

      #image-container {
        flex: 1;
        min-width: 300px;
        background: var(--bg-color);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        overflow: auto;
      }
      #diagram-image {
        max-width: 100%;
        max-height: 100%;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        background: white;
      }

      #footer {
        background: var(--bg-color);
        border-top: 1px solid var(--border-color);
        padding: 12px 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
        justify-content: center;
        box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
      }

      /* Button Styles */
      .btn {
        background: var(--button-bg);
        border: 1px solid var(--border-color);
        color: var(--text-color);
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        text-decoration: none;
        transition: all 0.2s;
        white-space: nowrap;
      }
      .btn:hover {
        background: var(--button-hover);
        border-color: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: var(--shadow);
      }
      .btn:active {
        transform: translateY(0);
        box-shadow: none;
      }
      .btn i {
        font-size: 12px;
      }

      #footer a {
        color: var(--primary-color);
        text-decoration: none;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }
      #footer a:hover {
        background: rgba(37, 99, 235, 0.1);
        transform: translateY(-1px);
      }

      .tooltip-container {
        display: flex;
        gap: 8px;
        align-items: center;
        margin-left: auto;
      }
      #encoded-input {
        background: var(--editor-bg);
        border: 1px solid var(--border-color);
        color: var(--text-color);
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 13px;
        min-width: 200px;
        font-family: monospace;
      }
      #encoded-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }
      .decode-btn {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        transition: all 0.2s;
      }
      .decode-btn:hover {
        background: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: var(--shadow);
      }
      .decode-btn:active {
        transform: translateY(0);
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        #container {
          flex-direction: column;
        }
        #resizer {
          width: 100%;
          height: 6px;
          cursor: row-resize;
        }
        #footer {
          padding: 8px 12px;
          gap: 6px;
        }
        .btn {
          padding: 6px 12px;
          font-size: 12px;
        }
        #encoded-input {
          min-width: 150px;
        }
        .tooltip-container {
          margin-left: 0;
          margin-top: 8px;
          width: 100%;
        }
      }

      /* Loading Animation */
      .loading {
        position: relative;
        overflow: hidden;
      }
      .loading::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(37, 99, 235, 0.2),
          transparent
        );
        animation: loading 1.5s infinite;
      }
      @keyframes loading {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      /* Accessibility */
      .btn:focus,
      .decode-btn:focus,
      #encoded-input:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
      }
      @media (prefers-reduced-motion: reduce) {
        * {
          animation: none !important;
          transition: none !important;
        }
      }

      /* Print Styles */
      @media print {
        #header,
        #footer {
          display: none;
        }
        #container {
          flex-direction: row;
        }
        #image-container {
          padding: 0;
        }
      }
    </style>

    <!-- Font Awesome Icons (minimal set) -->
    <style>
      .fas,
      .far,
      .fal,
      .fab,
      .fad,
      .fak:before {
        font-family: "FontAwesome";
        font-weight: 900;
        font-style: normal;
      }
      .fa-copy:before {
        content: "\f0c5";
      }
      .fa-paste:before {
        content: "\f0ea";
      }
      .fa-save:before {
        content: "\f0c7";
      }
      .fa-folder-open:before {
        content: "\f07c";
      }
      .fa-adjust:before {
        content: "\f042";
      }
      .fa-link:before {
        content: "\f0c9";
      }
      .fa-external-link:before {
        content: "\f08e";
      }
      .fa-image:before {
        content: "\f03e";
      }
      .fa-vector-square:before {
        content: "\f5cb";
      }
      .fa-font:before {
        content: "\f031";
      }
      .fa-unlock-alt:before {
        content: "\f13a";
      }
      .fa-markdown:before {
        content: "\f60f";
      }
      .fa-play:before {
        content: "\f04b";
      }
      .fa-moon:before {
        content: "\f186";
      }
      .fa-sun:before {
        content: "\f185";
      }
    </style>
  </head>

  <body>
    <!-- Modern Header -->
    <div id="header">
      <span>🌱 PlantUML Editor - Standalone</span>
      <button
        id="dark-mode-toggle"
        class="btn"
        onclick="toggleDarkMode()"
        title="Toggle Dark Mode (Ctrl+D)"
      >
        <i class="fas fa-moon"></i>
      </button>
    </div>

    <!-- Main Application Container -->
    <div id="external_container">
      <div id="container">
        <!-- Editor Panel -->
        <div id="editor-panel">
          <div id="editor" contenteditable="true" spellcheck="false">
            @startuml Alice -> Bob: Hello Bob -> Alice: Hi there! @enduml
          </div>
        </div>

        <!-- Resizer -->
        <div id="resizer"></div>

        <!-- Image Container -->
        <div id="image-container">
          <img
            id="diagram-image"
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkRpYWdyYW0gd2lsbCBhcHBlYXIgaGVyZTwvdGV4dD4KICA8L3N2Zz4="
            alt="PlantUML Diagram"
          />
        </div>
      </div>

      <!-- Footer/Toolbar -->
      <div id="footer">
        <button
          class="btn"
          onclick="generateDiagram()"
          title="Generate Diagram"
        >
          <i class="fas fa-play"></i> Generate
        </button>

        <button
          class="btn"
          onclick="copyToClipboard()"
          title="Copy to Clipboard"
        >
          <i class="fas fa-copy"></i> Copy
        </button>

        <button class="btn" onclick="saveDiagram()" title="Save Diagram">
          <i class="fas fa-save"></i> Save
        </button>

        <button class="btn" onclick="loadExample()" title="Load Example">
          <i class="fas fa-folder-open"></i> Example
        </button>

        <a
          href="#"
          class="btn"
          onclick="openInNewWindow()"
          title="Open in New Window"
        >
          <i class="fas fa-external-link"></i> New Window
        </a>

        <div class="tooltip-container">
          <input
            type="text"
            id="encoded-input"
            placeholder="Paste encoded PlantUML here..."
            title="Decode PlantUML from URL or encoded text"
          />
          <button
            class="decode-btn"
            onclick="decodeAndLoad()"
            title="Decode and Load"
          >
            <i class="fas fa-unlock-alt"></i> Decode
          </button>
        </div>
      </div>
    </div>

    <!-- JavaScript Functionality -->
    <script>
      // Dark mode functionality
      function toggleDarkMode() {
        document.body.classList.toggle("dark-mode");
        const isDark = document.body.classList.contains("dark-mode");
        localStorage.setItem("darkMode", isDark);
        updateDarkModeIcon(isDark);
      }

      function updateDarkModeIcon(isDark) {
        const icon = document.querySelector("#dark-mode-toggle i");
        if (icon) {
          icon.className = isDark ? "fas fa-sun" : "fas fa-moon";
        }
      }

      // Initialize dark mode from localStorage
      document.addEventListener("DOMContentLoaded", function () {
        const savedDarkMode = localStorage.getItem("darkMode") === "true";
        if (savedDarkMode) {
          document.body.classList.add("dark-mode");
        }
        updateDarkModeIcon(savedDarkMode);

        // Initialize resizer
        initializeResizer();

        // Auto-generate diagram on load
        generateDiagram();
      });

      // Resizer functionality
      function initializeResizer() {
        const resizer = document.getElementById("resizer");
        const editorPanel = document.getElementById("editor-panel");
        const imageContainer = document.getElementById("image-container");
        let isResizing = false;

        resizer.addEventListener("mousedown", function (e) {
          isResizing = true;
          document.addEventListener("mousemove", handleMouseMove);
          document.addEventListener("mouseup", handleMouseUp);
          e.preventDefault();
        });

        function handleMouseMove(e) {
          if (!isResizing) return;

          const container = document.getElementById("container");
          const containerRect = container.getBoundingClientRect();
          const newLeftWidth =
            ((e.clientX - containerRect.left) / containerRect.width) * 100;

          if (newLeftWidth > 20 && newLeftWidth < 80) {
            editorPanel.style.flex = `0 0 ${newLeftWidth}%`;
            imageContainer.style.flex = `0 0 ${100 - newLeftWidth}%`;
          }
        }

        function handleMouseUp() {
          isResizing = false;
          document.removeEventListener("mousemove", handleMouseMove);
          document.removeEventListener("mouseup", handleMouseUp);
        }
      }

      // PlantUML functionality
      function generateDiagram() {
        const editor = document.getElementById("editor");
        const imageContainer = document.getElementById("diagram-image");
        const plantUMLCode = editor.textContent || editor.innerText;

        // Add loading state
        imageContainer.classList.add("loading");

        // Encode PlantUML for URL
        const encoded = encodePlantUML(plantUMLCode);
        const plantUMLServerURL = "http://www.plantuml.com/plantuml/svg/";
        const diagramURL = plantUMLServerURL + encoded;

        // Update image
        imageContainer.src = diagramURL;
        imageContainer.onload = function () {
          imageContainer.classList.remove("loading");
        };
        imageContainer.onerror = function () {
          imageContainer.classList.remove("loading");
          imageContainer.src =
            "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmVjYWNhIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2VmNDQ0NCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkVycm9yIGdlbmVyYXRpbmcgZGlhZ3JhbTwvdGV4dD4KICA8L3N2Zz4=";
        };
      }

      // PlantUML encoding (simplified version)
      function encodePlantUML(plantuml) {
        // Simple base64 encoding for demo purposes
        // In a real implementation, you'd use the proper PlantUML encoding
        return btoa(unescape(encodeURIComponent(plantuml)))
          .replace(/\+/g, "-")
          .replace(/\//g, "_")
          .replace(/=/g, "");
      }

      function decodePlantUML(encoded) {
        // Simple base64 decoding for demo purposes
        try {
          const base64 = encoded.replace(/-/g, "+").replace(/_/g, "/");
          const padding = base64.length % 4;
          const padded = base64 + "=".repeat(padding ? 4 - padding : 0);
          return decodeURIComponent(escape(atob(padded)));
        } catch (e) {
          return "";
        }
      }

      // Utility functions
      function copyToClipboard() {
        const editor = document.getElementById("editor");
        const text = editor.textContent || editor.innerText;

        if (navigator.clipboard) {
          navigator.clipboard.writeText(text).then(() => {
            showNotification("Copied to clipboard!");
          });
        } else {
          // Fallback for older browsers
          const textArea = document.createElement("textarea");
          textArea.value = text;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand("copy");
          document.body.removeChild(textArea);
          showNotification("Copied to clipboard!");
        }
      }

      function saveDiagram() {
        const editor = document.getElementById("editor");
        const text = editor.textContent || editor.innerText;
        const blob = new Blob([text], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "diagram.puml";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        showNotification("Diagram saved!");
      }

      function loadExample() {
        const examples = [
          `@startuml
Alice -> Bob: Authentication Request
Bob --> Alice: Authentication Response

Alice -> Bob: Another authentication Request
Alice <-- Bob: Another authentication Response
@enduml`,
          `@startuml
participant User
participant "First Class" as A
participant "Second Class" as B
participant "Last Class" as C

User -> A: DoWork
activate A

A -> B: Create Request
activate B

B -> C: DoWork
activate C
C --> B: WorkDone
destroy C

B --> A: Request Created
deactivate B

A --> User: Done
deactivate A
@enduml`,
          `@startuml
start

:Read data;
:Generate diagrams;

if (Diagram OK?) then (yes)
  :Save diagram;
else (no)
  :Show error;
endif

stop
@enduml`,
        ];

        const randomExample =
          examples[Math.floor(Math.random() * examples.length)];
        const editor = document.getElementById("editor");
        editor.textContent = randomExample;
        generateDiagram();
        showNotification("Example loaded!");
      }

      function openInNewWindow() {
        const newWindow = window.open("", "_blank", "width=800,height=600");
        newWindow.document.write(document.documentElement.outerHTML);
        newWindow.document.close();
      }

      function decodeAndLoad() {
        const input = document.getElementById("encoded-input");
        const encoded = input.value.trim();

        if (!encoded) {
          showNotification("Please enter encoded PlantUML text");
          return;
        }

        const decoded = decodePlantUML(encoded);
        if (decoded) {
          const editor = document.getElementById("editor");
          editor.textContent = decoded;
          generateDiagram();
          input.value = "";
          showNotification("Diagram decoded and loaded!");
        } else {
          showNotification("Failed to decode PlantUML text");
        }
      }

      function showNotification(message) {
        // Simple notification system
        const notification = document.createElement("div");
        notification.textContent = message;
        notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--primary-color);
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;

        document.body.appendChild(notification);

        setTimeout(() => {
          notification.style.animation = "slideOut 0.3s ease";
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 300);
        }, 3000);
      }

      // Keyboard shortcuts
      document.addEventListener("keydown", function (e) {
        if (e.ctrlKey || e.metaKey) {
          switch (e.key) {
            case "d":
              e.preventDefault();
              toggleDarkMode();
              break;
            case "s":
              e.preventDefault();
              saveDiagram();
              break;
            case "Enter":
              e.preventDefault();
              generateDiagram();
              break;
          }
        }
      });

      // Auto-generate on editor change (debounced)
      let generateTimeout;
      document.getElementById("editor").addEventListener("input", function () {
        clearTimeout(generateTimeout);
        generateTimeout = setTimeout(generateDiagram, 1000);
      });

      // Add slide animations
      const style = document.createElement("style");
      style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
      document.head.appendChild(style);
    </script>
  </body>
</html>
